# Setup Email untuk Reset Password

## Konfigurasi Gmail SMTP

Untuk menggunakan Gmail sebagai SMTP server, i<PERSON><PERSON> langkah berikut:

### 1. Aktifkan 2-Factor Authentication di Gmail
- Masuk ke akun Google Anda
- Pergi ke Security settings
- Aktifkan 2-Step Verification

### 2. Buat App Password
- Di Google Account settings, pergi ke Security
- Pilih "App passwords"
- <PERSON><PERSON>h "Mail" dan "Other (custom name)"
- <PERSON><PERSON><PERSON><PERSON> nama "LaporinAja"
- Copy password yang dihasilkan

### 3. Update file .env
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password-here
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="LaporinAja"
```

### 4. Clear Config Cache
```bash
php artisan config:clear
php artisan cache:clear
```

## Alternatif: Menggunakan Mailtrap (untuk testing)

Jika ingin testing tanpa email asli:

1. Daftar di https://mailtrap.io
2. Buat inbox baru
3. Copy SMTP credentials
4. Update .env:

```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=your-mailtrap-username
MAIL_PASSWORD=your-mailtrap-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="LaporinAja"
```

## Testing Email

Untuk test apakah email berfungsi:

1. Pergi ke halaman forgot password
2. Masukkan email yang terdaftar
3. Cek inbox email atau Mailtrap
4. Klik link reset password
5. Masukkan password baru

## Troubleshooting

### Email tidak terkirim:
- Pastikan credentials benar
- Cek firewall/antivirus
- Pastikan less secure apps enabled (jika pakai Gmail)

### Error "Connection refused":
- Cek MAIL_HOST dan MAIL_PORT
- Pastikan internet connection stabil

### Link reset tidak bekerja:
- Cek APP_URL di .env
- Pastikan route password.reset ada
