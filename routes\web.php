<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\LaporanController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\FeedbackController;
use App\Http\Controllers\RatingController;

// ================= LANDING & TEST =================
Route::get('/', function () {
    return view('landing');
});
Route::get('/test', function () {
    return 'Laravel jalan!';
});

// ============= FEEDBACK CALL CENTER (UMUM) ============
Route::post('/feedback/callcenter/store', [FeedbackController::class, 'storeCallCenter'])->name('feedback.callcenter.store');

// ================= AUTHENTICATED ROUTES =================
Route::middleware(['auth'])->group(function () {

    // ---------------- PROFIL ----------------
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // ---------------- REDIRECT DASHBOARD ----------------
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // ---------------- FEEDBACK USER ----------------
    Route::post('/feedback/store', [FeedbackController::class, 'store'])->name('feedback.store');
    Route::post('/rating', [RatingController::class, 'store'])->name('rating.store');

    // ---------------- NOTIFICATIONS ----------------
    Route::post('/notifications/mark-as-read', [NotificationController::class, 'markAsRead'])->name('notifications.markAsRead');

    // ================= ADMIN ROUTES =================
    Route::middleware(['role:admin'])->group(function () {
        Route::get('/admin/dashboard', [DashboardController::class, 'admin'])->name('admin.dashboard');

        // Kelola akun petugas
        Route::get('/pengguna/create-petugas', [UserController::class, 'createPetugas'])->name('pengguna.create-petugas');
        Route::post('/pengguna/store-petugas', [UserController::class, 'storePetugas'])->name('pengguna.store-petugas');

        // Edit & Hapus pengguna
        Route::get('/pengguna/{user}/edit', [UserController::class, 'edit'])->name('pengguna.edit');
        Route::put('/pengguna/{user}', [UserController::class, 'update'])->name('pengguna.update');
        Route::delete('/pengguna/{user}', [UserController::class, 'destroy'])->name('pengguna.destroy');
    });

    // ================= PETUGAS ROUTES =================
    Route::middleware(['role:petugas'])->group(function () {
        Route::get('/petugas/dashboard', [DashboardController::class, 'petugas'])->name('dashboard.petugas');
    });

    // =========== ADMIN & PETUGAS - PENGGUNA & FEEDBACK ===========
    Route::middleware(['role:admin,petugas'])->group(function () {
        Route::get('/pengguna', [UserController::class, 'index'])->name('pengguna.index');
        Route::get('/feedback', [FeedbackController::class, 'index'])->name('feedback.index');
    });

    // ================= SUPERADMIN ROUTES =================
    Route::middleware(['role:superadmin'])->group(function () {
        Route::get('/superadmin/dashboard', [DashboardController::class, 'superadmin'])->name('dashboard.superadmin');
    });

    // ================= ADMIN & PETUGAS - LAPORAN =================
    Route::middleware(['role:admin,petugas'])->group(function () {
        Route::get('/laporan', [LaporanController::class, 'index'])->name('laporan.index');
        Route::get('/laporan/{laporan}', [LaporanController::class, 'show'])->name('laporan.show');

        // Proses laporan
        Route::get('/laporan/{id}/proses', [LaporanController::class, 'prosesForm'])->name('laporan.proses.form');
        Route::post('/laporan/{id}/proses', [LaporanController::class, 'proses'])->name('laporan.proses');

        // Selesaikan laporan
        Route::get('/laporan/{id}/selesai', [LaporanController::class, 'selesaiForm'])->name('laporan.selesai');
        Route::post('/laporan/{id}/selesai', [LaporanController::class, 'selesaikan']);

        // Update prioritas laporan
        Route::post('/laporan/{id}/prioritas', [LaporanController::class, 'updatePrioritas'])->name('laporan.prioritas');
    });

    // ================= USER ROUTES =================
    Route::middleware(['role:user'])->group(function () {
        Route::get('/user/dashboard', [DashboardController::class, 'user'])->name('user.dashboard');

        // Laporan warga
        Route::get('/user/laporan', [LaporanController::class, 'index'])->name('user.laporan.index');
        Route::get('/user/laporan/{laporan}', [LaporanController::class, 'show'])->name('user.laporan.show');
        Route::get('/lapor/create', [LaporanController::class, 'create'])->name('lapor.create');
        Route::post('/lapor/store', [LaporanController::class, 'store'])->name('lapor.store');
    });
});

// ================= AUTH ROUTES BAWAAN LARAVEL =================
require __DIR__.'/auth.php';
