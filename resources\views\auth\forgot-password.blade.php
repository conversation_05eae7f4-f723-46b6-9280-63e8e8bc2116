<x-guest-layout>
    <div class="w-full max-w-md bg-white p-8 rounded-xl shadow-lg mt-10">

        <!-- Back Button -->
        <div class="mb-4">
            <a href="{{ route('login') }}" class="inline-flex items-center text-blue-600 hover:text-blue-800 transition duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Ke<PERSON><PERSON> ke Login
            </a>
        </div>

        <h1 class="text-2xl font-bold text-blue-700 mb-2 text-center">Lupa Password?</h1>

        <div class="mb-6 text-sm text-gray-600 text-center">
            Tidak masalah! Masukkan alamat email Anda dan kami akan mengirimkan tautan untuk mengatur ulang password Anda.
        </div>

        <!-- Session Status -->
        @if (session('status'))
            <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg text-sm">
                <p class="text-center font-medium">{{ session('status') }}</p>

                @if (session('reset_link'))
                    <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <p class="text-sm text-blue-800 mb-2"><strong>Link Reset Password (Development Mode):</strong></p>
                        <div class="bg-white p-2 rounded border text-xs break-all">
                            <a href="{{ session('reset_link') }}" class="text-blue-600 hover:underline">
                                {{ session('reset_link') }}
                            </a>
                        </div>
                        <p class="text-xs text-blue-600 mt-2">Klik link di atas atau copy-paste ke browser baru</p>
                    </div>
                @endif
            </div>
        @endif

        <form method="POST" action="{{ route('password.email') }}" class="space-y-4">
            @csrf

            <!-- Email Address -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">Alamat Email</label>
                <input id="email" type="email" name="email" value="{{ old('email') }}" required autofocus
                    class="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Masukkan email Anda" />
                @if ($errors->get('email'))
                    <div class="mt-2 text-sm text-red-600">
                        @foreach ($errors->get('email') as $error)
                            <p>{{ $error }}</p>
                        @endforeach
                    </div>
                @endif
            </div>

            <div>
                <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                    Kirim Link Reset Password
                </button>
            </div>
        </form>

        <!-- <p class="text-sm text-center mt-4">
            Ingat password Anda? <a href="{{ route('login') }}" class="text-blue-600 hover:underline">Kembali ke Login</a>
        </p> -->
    </div>
</x-guest-layout>
