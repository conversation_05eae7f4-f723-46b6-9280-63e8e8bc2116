<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Auth;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Biar variabel $notifications selalu ada di template dashboard
        View::composer('dashboard.template', function ($view) {
            $notifications = [];

            if (Auth::check()) {
                $notifications = Auth::user()->notifications()->take(10)->get();
            }

            $view->with('notifications', $notifications);
        });
    }
}
