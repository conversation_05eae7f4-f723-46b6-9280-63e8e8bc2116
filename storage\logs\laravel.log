[2025-07-02 13:35:55] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:79)
[stacktrace]
#0 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():77}(NULL)
#1 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(77): tap(NULL, Object(Closure))
#2 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(172): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\LaporinAja\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\laragon\\\\www\\\\...')
#43 {main}
"} 
[2025-07-02 13:35:55] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:79)
[stacktrace]
#0 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::key():77}(NULL)
#1 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(77): tap(NULL, Object(Closure))
#2 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Encryption\\EncryptionServiceProvider->{closure:Illuminate\\Encryption\\EncryptionServiceProvider::registerEncrypter():29}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(255): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 C:\\laragon\\www\\LaporinAja\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\laragon\\\\www\\\\...')
#20 {main}
"} 
[2025-07-02 13:39:10] local.ERROR: Vite manifest not found at: C:\laragon\www\LaporinAja\public\build/manifest.json {"view":{"view":"C:\\laragon\\www\\LaporinAja\\resources\\views\\layouts\\guest.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1945471645 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#355</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1945471645\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","componentName":"<pre class=sf-dump id=sf-dump-275263328 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"12 characters\">guest-layout</span>\"
</pre><script>Sfdump(\"sf-dump-275263328\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","attributes":"<pre class=sf-dump id=sf-dump-871240539 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\View\\ComponentAttributeBag</span> {<a class=sf-dump-ref>#381</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-871240539\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","slot":"<pre class=sf-dump id=sf-dump-53665295 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\View\\ComponentSlot</span> {<a class=sf-dump-ref>#387</a><samp data-depth=1 class=sf-dump-expanded>
  +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentAttributeBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentAttributeBag</span></span> {<a class=sf-dump-ref>#377</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">contents</span>: \"\"\"
    <span class=sf-dump-str title=\"2970 characters\">&lt;div class=&quot;w-full max-w-md bg-white p-8 rounded-xl shadow-lg mt-10&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">        &lt;!-- Back Button --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">        &lt;div class=&quot;mb-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;a href=&quot;http://127.0.0.1:8000&quot; class=&quot;inline-flex items-center text-black hover:text-blue-800 transition duration-200&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;svg class=&quot;w-4 h-4 mr-2&quot; fill=&quot;none&quot; stroke=&quot;currentColor&quot; viewBox=&quot;0 0 24 24&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    &lt;path stroke-linecap=&quot;round&quot; stroke-linejoin=&quot;round&quot; stroke-width=&quot;2&quot; d=&quot;M10 19l-7-7m0 0l7-7m-7 7h18&quot;&gt;&lt;/path&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;/svg&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                Kembali ke Beranda<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">        &lt;h1 class=&quot;text-2xl font-bold text-black mb-6 text-center&quot;&gt;Login LaporinAja&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">        &lt;form method=&quot;POST&quot; action=&quot;http://127.0.0.1:8000/login&quot; class=&quot;space-y-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;input type=&quot;hidden&quot; name=&quot;_token&quot; value=&quot;lSVpnp8zjvx0jCccbQdmBUc8eF4mMAb8PyGprbFy&quot; autocomplete=&quot;off&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Email Address --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;label for=&quot;email&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Email&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;input id=&quot;email&quot; type=&quot;email&quot; name=&quot;email&quot; :value=&quot;old(&#039;email&#039;)&quot; required autofocus autocomplete=&quot;username&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Password --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;label for=&quot;password&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Password&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;input id=&quot;password&quot; type=&quot;password&quot; name=&quot;password&quot; required autocomplete=&quot;current-password&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Remember Me --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;div class=&quot;flex items-center&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;input id=&quot;remember_me&quot; type=&quot;checkbox&quot; name=&quot;remember&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;label for=&quot;remember_me&quot; class=&quot;ml-2 block text-sm text-gray-700&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    Remember me<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Tombol --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;button type=&quot;submit&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    Login<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;p class=&quot;text-sm text-center mt-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                Belum punya akun? &lt;a href=&quot;http://127.0.0.1:8000/register&quot; class=&quot;text-blue-600 hover:underline&quot;&gt;Daftar&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                            &lt;p class=&quot;text-sm text-center mt-2&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    &lt;a class=&quot;text-blue-600 hover:underline&quot; href=&quot;http://127.0.0.1:8000/forgot-password&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                        Lupa password?<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    &lt;/form&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">    &lt;/div&gt;</span>
    \"\"\"
</samp>}
</pre><script>Sfdump(\"sf-dump-53665295\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__laravel_slots":"<pre class=sf-dump id=sf-dump-1640782023 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>__default</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentSlot
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentSlot</span></span> {<a class=sf-dump-ref>#387</a><samp data-depth=2 class=sf-dump-compact>
    +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentAttributeBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentAttributeBag</span></span> {<a class=sf-dump-ref>#377</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
    </samp>}
    #<span class=sf-dump-protected title=\"Protected property\">contents</span>: \"\"\"
      <span class=sf-dump-str title=\"2970 characters\">&lt;div class=&quot;w-full max-w-md bg-white p-8 rounded-xl shadow-lg mt-10&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">        &lt;!-- Back Button --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">        &lt;div class=&quot;mb-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;a href=&quot;http://127.0.0.1:8000&quot; class=&quot;inline-flex items-center text-black hover:text-blue-800 transition duration-200&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;svg class=&quot;w-4 h-4 mr-2&quot; fill=&quot;none&quot; stroke=&quot;currentColor&quot; viewBox=&quot;0 0 24 24&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    &lt;path stroke-linecap=&quot;round&quot; stroke-linejoin=&quot;round&quot; stroke-width=&quot;2&quot; d=&quot;M10 19l-7-7m0 0l7-7m-7 7h18&quot;&gt;&lt;/path&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;/svg&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                Kembali ke Beranda<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">        &lt;h1 class=&quot;text-2xl font-bold text-black mb-6 text-center&quot;&gt;Login LaporinAja&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">        &lt;form method=&quot;POST&quot; action=&quot;http://127.0.0.1:8000/login&quot; class=&quot;space-y-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;input type=&quot;hidden&quot; name=&quot;_token&quot; value=&quot;lSVpnp8zjvx0jCccbQdmBUc8eF4mMAb8PyGprbFy&quot; autocomplete=&quot;off&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Email Address --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;label for=&quot;email&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Email&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;input id=&quot;email&quot; type=&quot;email&quot; name=&quot;email&quot; :value=&quot;old(&#039;email&#039;)&quot; required autofocus autocomplete=&quot;username&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Password --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;label for=&quot;password&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Password&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;input id=&quot;password&quot; type=&quot;password&quot; name=&quot;password&quot; required autocomplete=&quot;current-password&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Remember Me --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;div class=&quot;flex items-center&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;input id=&quot;remember_me&quot; type=&quot;checkbox&quot; name=&quot;remember&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;label for=&quot;remember_me&quot; class=&quot;ml-2 block text-sm text-gray-700&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    Remember me<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Tombol --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;button type=&quot;submit&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    Login<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;p class=&quot;text-sm text-center mt-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                Belum punya akun? &lt;a href=&quot;http://127.0.0.1:8000/register&quot; class=&quot;text-blue-600 hover:underline&quot;&gt;Daftar&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                            &lt;p class=&quot;text-sm text-center mt-2&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    &lt;a class=&quot;text-blue-600 hover:underline&quot; href=&quot;http://127.0.0.1:8000/forgot-password&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                        Lupa password?<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    &lt;/form&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">    &lt;/div&gt;</span>
      \"\"\"
  </samp>}
</samp>]
</pre><script>Sfdump(\"sf-dump-1640782023\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Vite manifest not found at: C:\\laragon\\www\\LaporinAja\\public\\build/manifest.json at C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:728)
[stacktrace]
#0 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(293): Illuminate\\Foundation\\Vite->manifest('build')
#1 C:\\laragon\\www\\LaporinAja\\resources\\views\\layouts\\guest.blade.php(15): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon\\\\www\\\\...')
#3 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon\\\\www\\\\...', Array)
#5 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#6 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#7 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#10 C:\\laragon\\www\\LaporinAja\\resources\\views\\auth\\login.blade.php(68): Illuminate\\View\\Factory->renderComponent()
#11 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon\\\\www\\\\...')
#12 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#13 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon\\\\www\\\\...', Array)
#14 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#15 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#16 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#19 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#20 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#21 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#23 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\LaporinAja\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(28): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\LaporinAja\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\laragon\\\\www\\\\...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\laragon\\www\\LaporinAja\\public\\build/manifest.json at C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:728)
[stacktrace]
#0 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(293): Illuminate\\Foundation\\Vite->manifest('build')
#1 C:\\laragon\\www\\LaporinAja\\storage\\framework\\views\\c6a2be6ead46b6f95a1a011982ac6c76.php(15): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon\\\\www\\\\...')
#3 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon\\\\www\\\\...', Array)
#5 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#6 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#7 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#10 C:\\laragon\\www\\LaporinAja\\storage\\framework\\views\\bcac8a10db8bd79a5fbe190faad68d17.php(119): Illuminate\\View\\Factory->renderComponent()
#11 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon\\\\www\\\\...')
#12 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#13 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon\\\\www\\\\...', Array)
#14 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#15 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#16 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#19 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#20 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#21 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#23 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\LaporinAja\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(28): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\LaporinAja\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\laragon\\\\www\\\\...')
#65 {main}
"} 
[2025-07-02 13:39:26] local.ERROR: Vite manifest not found at: C:\laragon\www\LaporinAja\public\build/manifest.json {"view":{"view":"C:\\laragon\\www\\LaporinAja\\resources\\views\\layouts\\guest.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-379357620 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#356</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-379357620\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","componentName":"<pre class=sf-dump id=sf-dump-385323088 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"12 characters\">guest-layout</span>\"
</pre><script>Sfdump(\"sf-dump-385323088\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","attributes":"<pre class=sf-dump id=sf-dump-571171574 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\View\\ComponentAttributeBag</span> {<a class=sf-dump-ref>#381</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-571171574\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","slot":"<pre class=sf-dump id=sf-dump-821293877 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\View\\ComponentSlot</span> {<a class=sf-dump-ref>#379</a><samp data-depth=1 class=sf-dump-expanded>
  +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentAttributeBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentAttributeBag</span></span> {<a class=sf-dump-ref>#387</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">contents</span>: \"\"\"
    <span class=sf-dump-str title=\"3472 characters\">&lt;div class=&quot;w-full max-w-md bg-white p-8 rounded-xl shadow-lg mt-10&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">        &lt;!-- Back Button --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">        &lt;div class=&quot;mb-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;a href=&quot;http://127.0.0.1:8000&quot; class=&quot;inline-flex items-center text-blue-600 hover:text-blue-800 transition duration-200&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                &lt;svg class=&quot;w-4 h-4 mr-2&quot; fill=&quot;none&quot; stroke=&quot;currentColor&quot; viewBox=&quot;0 0 24 24&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                    &lt;path stroke-linecap=&quot;round&quot; stroke-linejoin=&quot;round&quot; stroke-width=&quot;2&quot; d=&quot;M10 19l-7-7m0 0l7-7m-7 7h18&quot;&gt;&lt;/path&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                &lt;/svg&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                Kembali ke Beranda<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">        &lt;h1 class=&quot;text-2xl font-bold text-blue-700 mb-2 text-center&quot;&gt;Daftar Sebagai Warga&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">        &lt;p class=&quot;text-sm text-gray-600 mb-6 text-center&quot;&gt;Buat akun untuk melaporkan masalah di kampung Anda&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">        &lt;form method=&quot;POST&quot; action=&quot;http://127.0.0.1:8000/register&quot; class=&quot;space-y-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;input type=&quot;hidden&quot; name=&quot;_token&quot; value=&quot;lSVpnp8zjvx0jCccbQdmBUc8eF4mMAb8PyGprbFy&quot; autocomplete=&quot;off&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;!-- Hidden Role Field - Default to &#039;user&#039; --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;input type=&quot;hidden&quot; name=&quot;role&quot; value=&quot;user&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;!-- Name --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                &lt;label for=&quot;name&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Nama&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                &lt;input id=&quot;name&quot; type=&quot;text&quot; name=&quot;name&quot; :value=&quot;old(&#039;name&#039;)&quot; required autofocus autocomplete=&quot;name&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;!-- Email --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                &lt;label for=&quot;email&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Email&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                &lt;input id=&quot;email&quot; type=&quot;email&quot; name=&quot;email&quot; :value=&quot;old(&#039;email&#039;)&quot; required autocomplete=&quot;username&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;!-- Password --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                &lt;label for=&quot;password&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Password&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                &lt;input id=&quot;password&quot; type=&quot;password&quot; name=&quot;password&quot; required autocomplete=&quot;new-password&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;!-- Confirm Password --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                &lt;label for=&quot;password_confirmation&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Konfirmasi Password&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                &lt;input id=&quot;password_confirmation&quot; type=&quot;password&quot; name=&quot;password_confirmation&quot; required autocomplete=&quot;new-password&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;!-- Tombol --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                &lt;button type=&quot;submit&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                    class=&quot;w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                    Daftar<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                &lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;p class=&quot;text-sm text-center mt-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">                Sudah terdaftar? &lt;a href=&quot;http://127.0.0.1:8000/login&quot; class=&quot;text-blue-600 hover:underline&quot;&gt;Masuk di sini.&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">        &lt;/form&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"3472 characters\">    &lt;/div&gt;</span>
    \"\"\"
</samp>}
</pre><script>Sfdump(\"sf-dump-821293877\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__laravel_slots":"<pre class=sf-dump id=sf-dump-1563965389 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>__default</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentSlot
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentSlot</span></span> {<a class=sf-dump-ref>#379</a><samp data-depth=2 class=sf-dump-compact>
    +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentAttributeBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentAttributeBag</span></span> {<a class=sf-dump-ref>#387</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
    </samp>}
    #<span class=sf-dump-protected title=\"Protected property\">contents</span>: \"\"\"
      <span class=sf-dump-str title=\"3472 characters\">&lt;div class=&quot;w-full max-w-md bg-white p-8 rounded-xl shadow-lg mt-10&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">        &lt;!-- Back Button --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">        &lt;div class=&quot;mb-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;a href=&quot;http://127.0.0.1:8000&quot; class=&quot;inline-flex items-center text-blue-600 hover:text-blue-800 transition duration-200&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                &lt;svg class=&quot;w-4 h-4 mr-2&quot; fill=&quot;none&quot; stroke=&quot;currentColor&quot; viewBox=&quot;0 0 24 24&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                    &lt;path stroke-linecap=&quot;round&quot; stroke-linejoin=&quot;round&quot; stroke-width=&quot;2&quot; d=&quot;M10 19l-7-7m0 0l7-7m-7 7h18&quot;&gt;&lt;/path&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                &lt;/svg&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                Kembali ke Beranda<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">        &lt;h1 class=&quot;text-2xl font-bold text-blue-700 mb-2 text-center&quot;&gt;Daftar Sebagai Warga&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">        &lt;p class=&quot;text-sm text-gray-600 mb-6 text-center&quot;&gt;Buat akun untuk melaporkan masalah di kampung Anda&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">        &lt;form method=&quot;POST&quot; action=&quot;http://127.0.0.1:8000/register&quot; class=&quot;space-y-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;input type=&quot;hidden&quot; name=&quot;_token&quot; value=&quot;lSVpnp8zjvx0jCccbQdmBUc8eF4mMAb8PyGprbFy&quot; autocomplete=&quot;off&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;!-- Hidden Role Field - Default to &#039;user&#039; --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;input type=&quot;hidden&quot; name=&quot;role&quot; value=&quot;user&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;!-- Name --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                &lt;label for=&quot;name&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Nama&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                &lt;input id=&quot;name&quot; type=&quot;text&quot; name=&quot;name&quot; :value=&quot;old(&#039;name&#039;)&quot; required autofocus autocomplete=&quot;name&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;!-- Email --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                &lt;label for=&quot;email&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Email&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                &lt;input id=&quot;email&quot; type=&quot;email&quot; name=&quot;email&quot; :value=&quot;old(&#039;email&#039;)&quot; required autocomplete=&quot;username&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;!-- Password --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                &lt;label for=&quot;password&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Password&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                &lt;input id=&quot;password&quot; type=&quot;password&quot; name=&quot;password&quot; required autocomplete=&quot;new-password&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;!-- Confirm Password --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                &lt;label for=&quot;password_confirmation&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Konfirmasi Password&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                &lt;input id=&quot;password_confirmation&quot; type=&quot;password&quot; name=&quot;password_confirmation&quot; required autocomplete=&quot;new-password&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;!-- Tombol --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                &lt;button type=&quot;submit&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                    class=&quot;w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                    Daftar<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                &lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;p class=&quot;text-sm text-center mt-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">                Sudah terdaftar? &lt;a href=&quot;http://127.0.0.1:8000/login&quot; class=&quot;text-blue-600 hover:underline&quot;&gt;Masuk di sini.&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">        &lt;/form&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"3472 characters\">    &lt;/div&gt;</span>
      \"\"\"
  </samp>}
</samp>]
</pre><script>Sfdump(\"sf-dump-1563965389\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Vite manifest not found at: C:\\laragon\\www\\LaporinAja\\public\\build/manifest.json at C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:728)
[stacktrace]
#0 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(293): Illuminate\\Foundation\\Vite->manifest('build')
#1 C:\\laragon\\www\\LaporinAja\\resources\\views\\layouts\\guest.blade.php(15): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon\\\\www\\\\...')
#3 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon\\\\www\\\\...', Array)
#5 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#6 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#7 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#10 C:\\laragon\\www\\LaporinAja\\resources\\views\\auth\\register.blade.php(63): Illuminate\\View\\Factory->renderComponent()
#11 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon\\\\www\\\\...')
#12 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#13 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon\\\\www\\\\...', Array)
#14 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#15 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#16 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#19 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#20 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#21 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#23 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\LaporinAja\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(28): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\LaporinAja\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\laragon\\\\www\\\\...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\laragon\\www\\LaporinAja\\public\\build/manifest.json at C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:728)
[stacktrace]
#0 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(293): Illuminate\\Foundation\\Vite->manifest('build')
#1 C:\\laragon\\www\\LaporinAja\\storage\\framework\\views\\c6a2be6ead46b6f95a1a011982ac6c76.php(15): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon\\\\www\\\\...')
#3 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon\\\\www\\\\...', Array)
#5 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#6 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#7 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#10 C:\\laragon\\www\\LaporinAja\\storage\\framework\\views\\6c27ea660f7950d7da31a6e54b862618.php(133): Illuminate\\View\\Factory->renderComponent()
#11 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon\\\\www\\\\...')
#12 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#13 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon\\\\www\\\\...', Array)
#14 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#15 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#16 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#19 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#20 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#21 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#23 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\LaporinAja\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(28): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\LaporinAja\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\laragon\\\\www\\\\...')
#65 {main}
"} 
[2025-07-02 13:39:30] local.ERROR: Vite manifest not found at: C:\laragon\www\LaporinAja\public\build/manifest.json {"view":{"view":"C:\\laragon\\www\\LaporinAja\\resources\\views\\layouts\\guest.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-62280327 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#355</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-62280327\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","componentName":"<pre class=sf-dump id=sf-dump-570015103 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"12 characters\">guest-layout</span>\"
</pre><script>Sfdump(\"sf-dump-570015103\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","attributes":"<pre class=sf-dump id=sf-dump-2018500017 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\View\\ComponentAttributeBag</span> {<a class=sf-dump-ref>#377</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-2018500017\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","slot":"<pre class=sf-dump id=sf-dump-1230427251 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\View\\ComponentSlot</span> {<a class=sf-dump-ref>#387</a><samp data-depth=1 class=sf-dump-expanded>
  +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentAttributeBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentAttributeBag</span></span> {<a class=sf-dump-ref>#380</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">contents</span>: \"\"\"
    <span class=sf-dump-str title=\"2970 characters\">&lt;div class=&quot;w-full max-w-md bg-white p-8 rounded-xl shadow-lg mt-10&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">        &lt;!-- Back Button --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">        &lt;div class=&quot;mb-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;a href=&quot;http://127.0.0.1:8000&quot; class=&quot;inline-flex items-center text-black hover:text-blue-800 transition duration-200&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;svg class=&quot;w-4 h-4 mr-2&quot; fill=&quot;none&quot; stroke=&quot;currentColor&quot; viewBox=&quot;0 0 24 24&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    &lt;path stroke-linecap=&quot;round&quot; stroke-linejoin=&quot;round&quot; stroke-width=&quot;2&quot; d=&quot;M10 19l-7-7m0 0l7-7m-7 7h18&quot;&gt;&lt;/path&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;/svg&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                Kembali ke Beranda<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">        &lt;h1 class=&quot;text-2xl font-bold text-black mb-6 text-center&quot;&gt;Login LaporinAja&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">        &lt;form method=&quot;POST&quot; action=&quot;http://127.0.0.1:8000/login&quot; class=&quot;space-y-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;input type=&quot;hidden&quot; name=&quot;_token&quot; value=&quot;lSVpnp8zjvx0jCccbQdmBUc8eF4mMAb8PyGprbFy&quot; autocomplete=&quot;off&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Email Address --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;label for=&quot;email&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Email&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;input id=&quot;email&quot; type=&quot;email&quot; name=&quot;email&quot; :value=&quot;old(&#039;email&#039;)&quot; required autofocus autocomplete=&quot;username&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Password --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;label for=&quot;password&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Password&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;input id=&quot;password&quot; type=&quot;password&quot; name=&quot;password&quot; required autocomplete=&quot;current-password&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Remember Me --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;div class=&quot;flex items-center&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;input id=&quot;remember_me&quot; type=&quot;checkbox&quot; name=&quot;remember&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;label for=&quot;remember_me&quot; class=&quot;ml-2 block text-sm text-gray-700&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    Remember me<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Tombol --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;button type=&quot;submit&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    Login<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;p class=&quot;text-sm text-center mt-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                Belum punya akun? &lt;a href=&quot;http://127.0.0.1:8000/register&quot; class=&quot;text-blue-600 hover:underline&quot;&gt;Daftar&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                            &lt;p class=&quot;text-sm text-center mt-2&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    &lt;a class=&quot;text-blue-600 hover:underline&quot; href=&quot;http://127.0.0.1:8000/forgot-password&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                        Lupa password?<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">                    &lt;/form&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
    <span class=sf-dump-str title=\"2970 characters\">    &lt;/div&gt;</span>
    \"\"\"
</samp>}
</pre><script>Sfdump(\"sf-dump-1230427251\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__laravel_slots":"<pre class=sf-dump id=sf-dump-568072826 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>__default</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentSlot
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentSlot</span></span> {<a class=sf-dump-ref>#387</a><samp data-depth=2 class=sf-dump-compact>
    +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentAttributeBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentAttributeBag</span></span> {<a class=sf-dump-ref>#380</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
    </samp>}
    #<span class=sf-dump-protected title=\"Protected property\">contents</span>: \"\"\"
      <span class=sf-dump-str title=\"2970 characters\">&lt;div class=&quot;w-full max-w-md bg-white p-8 rounded-xl shadow-lg mt-10&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">        &lt;!-- Back Button --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">        &lt;div class=&quot;mb-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;a href=&quot;http://127.0.0.1:8000&quot; class=&quot;inline-flex items-center text-black hover:text-blue-800 transition duration-200&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;svg class=&quot;w-4 h-4 mr-2&quot; fill=&quot;none&quot; stroke=&quot;currentColor&quot; viewBox=&quot;0 0 24 24&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    &lt;path stroke-linecap=&quot;round&quot; stroke-linejoin=&quot;round&quot; stroke-width=&quot;2&quot; d=&quot;M10 19l-7-7m0 0l7-7m-7 7h18&quot;&gt;&lt;/path&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;/svg&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                Kembali ke Beranda<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">        &lt;h1 class=&quot;text-2xl font-bold text-black mb-6 text-center&quot;&gt;Login LaporinAja&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">        &lt;form method=&quot;POST&quot; action=&quot;http://127.0.0.1:8000/login&quot; class=&quot;space-y-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;input type=&quot;hidden&quot; name=&quot;_token&quot; value=&quot;lSVpnp8zjvx0jCccbQdmBUc8eF4mMAb8PyGprbFy&quot; autocomplete=&quot;off&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Email Address --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;label for=&quot;email&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Email&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;input id=&quot;email&quot; type=&quot;email&quot; name=&quot;email&quot; :value=&quot;old(&#039;email&#039;)&quot; required autofocus autocomplete=&quot;username&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Password --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;label for=&quot;password&quot; class=&quot;block text-sm font-medium text-gray-700&quot;&gt;Password&lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;input id=&quot;password&quot; type=&quot;password&quot; name=&quot;password&quot; required autocomplete=&quot;current-password&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500&quot; /&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Remember Me --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;div class=&quot;flex items-center&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;input id=&quot;remember_me&quot; type=&quot;checkbox&quot; name=&quot;remember&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;label for=&quot;remember_me&quot; class=&quot;ml-2 block text-sm text-gray-700&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    Remember me<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;/label&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;!-- Tombol --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;button type=&quot;submit&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    class=&quot;w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    Login<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;p class=&quot;text-sm text-center mt-4&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                Belum punya akun? &lt;a href=&quot;http://127.0.0.1:8000/register&quot; class=&quot;text-blue-600 hover:underline&quot;&gt;Daftar&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                            &lt;p class=&quot;text-sm text-center mt-2&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    &lt;a class=&quot;text-blue-600 hover:underline&quot; href=&quot;http://127.0.0.1:8000/forgot-password&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                        Lupa password?<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">                    &lt;/form&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"2970 characters\">    &lt;/div&gt;</span>
      \"\"\"
  </samp>}
</samp>]
</pre><script>Sfdump(\"sf-dump-568072826\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Vite manifest not found at: C:\\laragon\\www\\LaporinAja\\public\\build/manifest.json at C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:728)
[stacktrace]
#0 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(293): Illuminate\\Foundation\\Vite->manifest('build')
#1 C:\\laragon\\www\\LaporinAja\\resources\\views\\layouts\\guest.blade.php(15): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon\\\\www\\\\...')
#3 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon\\\\www\\\\...', Array)
#5 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#6 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#7 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#10 C:\\laragon\\www\\LaporinAja\\resources\\views\\auth\\login.blade.php(68): Illuminate\\View\\Factory->renderComponent()
#11 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon\\\\www\\\\...')
#12 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#13 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon\\\\www\\\\...', Array)
#14 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#15 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#16 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#19 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#20 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#21 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#23 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\LaporinAja\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(28): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\LaporinAja\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\laragon\\\\www\\\\...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\laragon\\www\\LaporinAja\\public\\build/manifest.json at C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:728)
[stacktrace]
#0 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(293): Illuminate\\Foundation\\Vite->manifest('build')
#1 C:\\laragon\\www\\LaporinAja\\storage\\framework\\views\\c6a2be6ead46b6f95a1a011982ac6c76.php(15): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon\\\\www\\\\...')
#3 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon\\\\www\\\\...', Array)
#5 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#6 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#7 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(99): Illuminate\\View\\View->render()
#10 C:\\laragon\\www\\LaporinAja\\storage\\framework\\views\\bcac8a10db8bd79a5fbe190faad68d17.php(119): Illuminate\\View\\Factory->renderComponent()
#11 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon\\\\www\\\\...')
#12 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#13 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon\\\\www\\\\...', Array)
#14 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon\\\\www\\\\...', Array)
#15 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon\\\\www\\\\...', Array)
#16 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#19 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#20 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#21 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#23 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\LaporinAja\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(28): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\LaporinAja\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\LaporinAja\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\laragon\\\\www\\\\...')
#65 {main}
"} 
