<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Laporan;

class LaporanDiproses extends Notification
{
    use Queueable;

    protected $laporan;

    /**
     * Create a new notification instance.
     */
    public function __construct(Laporan $laporan)
    {
        $this->laporan = $laporan;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
                    ->subject('Laporan Anda Sedang Diproses')
                    ->greeting('Halo ' . $notifiable->name . ',')
                    ->line('Laporan Anda dengan judul "' . $this->laporan->judul . '" sedang diproses oleh petugas.')
                    ->action('Lihat Detail Laporan', url('/user/laporan/' . $this->laporan->id))
                    ->line('Terima kasih telah menggunakan LaporinAja.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'pesan' => 'Laporan "' . $this->laporan->judul . '" sedang diproses.',
            'laporan_id' => $this->laporan->id,
        ];
    }
}
