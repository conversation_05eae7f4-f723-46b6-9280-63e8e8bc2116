<!-- Success Modal Component -->
<div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-8 max-w-sm w-full mx-4 text-center">
        <!-- Success Icon -->
        <div id="modalIcon" class="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
        </div>

        <!-- Title -->
        <h3 id="modalTitle" class="text-xl font-semibold text-gray-800 mb-2">Berhasil</h3>

        <!-- Message -->
        <p id="modalMessage" class="text-gray-600 mb-6">Berhasil Menambahkan Satuan</p>

        <!-- OK Button -->
        <button onclick="closeSuccessModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition duration-200">
            OK
        </button>
    </div>
</div>

<script>
function showSuccessModal(title = 'Berhasil', message = 'Operasi berhasil dilakukan', type = 'success') {
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalMessage').textContent = message;

    // Update icon and colors based on type
    const iconDiv = document.getElementById('modalIcon');
    const iconSvg = iconDiv.querySelector('svg');

    if (type === 'laporan') {
        iconDiv.className = 'w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center';
        iconSvg.className = 'w-8 h-8 text-blue-600';
        iconSvg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>';
    } else if (type === 'petugas') {
        iconDiv.className = 'w-16 h-16 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center';
        iconSvg.className = 'w-8 h-8 text-purple-600';
        iconSvg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>';
    } else {
        // Default success
        iconDiv.className = 'w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center';
        iconSvg.className = 'w-8 h-8 text-green-600';
        iconSvg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>';
    }

    document.getElementById('successModal').classList.remove('hidden');

    // Auto close after 3 seconds
    setTimeout(() => {
        closeSuccessModal();
    }, 3000);
}

function closeSuccessModal() {
    document.getElementById('successModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('successModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSuccessModal();
    }
});

// Check for success message from session
<?php if(session('success')): ?>
    document.addEventListener('DOMContentLoaded', function() {
        showSuccessModal('Berhasil', '<?php echo e(session('success')); ?>');
    });
<?php endif; ?>

<?php if(session('success_title') && session('success_message')): ?>
    document.addEventListener('DOMContentLoaded', function() {
        const type = '<?php echo e(session('success_type', 'success')); ?>';
        showSuccessModal('<?php echo e(session('success_title')); ?>', '<?php echo e(session('success_message')); ?>', type);
    });
<?php endif; ?>
</script>
<?php /**PATH C:\laragon\www\LaporinAja\resources\views/components/success-modal.blade.php ENDPATH**/ ?>