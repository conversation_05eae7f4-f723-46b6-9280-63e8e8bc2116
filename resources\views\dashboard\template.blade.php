<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>LaporinAja - @yield('title')</title>

    <!-- ✅ Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        dark: {
                            bg: '#1f2937',
                            surface: '#374151',
                            text: '#f9fafb'
                        }
                    }
                }
            }
        }

        // Apply theme immediately to prevent flash
        (function() {
            const theme = localStorage.getItem('theme') || 'light';
            console.log('Initial theme load:', theme);
            if (theme === 'dark') {
                document.body.classList.add('dark-mode');
                console.log('Applied dark-mode class to body immediately');
            } else {
                document.body.classList.remove('dark-mode');
                console.log('Removed dark-mode class from body immediately');
            }
        })();
    </script>

    <!-- ✅ Font Awesome (Tambahan penting!) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        integrity="sha512-papSkZ6T3oO8r9HcZBkGWBkDNk3df2A0fGsUgXqpTHrU5LHTJo0lWZTnfl5zOq+sfDJzPqhCk1f2z9l3N9w5xg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- ✅ Style lokal -->
    <link rel="stylesheet" href="{{ asset('css/style.css') }}">

    <!-- Complete Dark Mode CSS -->
    <style>
        /* Light mode (default) */
        body {
            background-color: #eff6ff !important;
            color: #1f2937 !important;
            transition: all 0.3s ease;
        }

        nav {
            background-color: white !important;
            transition: all 0.3s ease;
        }

        section {
            background-color: transparent !important;
        }

        footer {
            background-color: #111827 !important;
            color: #d1d5db !important;
        }

        /* Dark mode styles */
        body.dark-mode {
            background-color: #111827 !important;
            color: #f9fafb !important;
        }

        body.dark-mode nav {
            background-color: #1f2937 !important;
        }

        body.dark-mode section {
            background-color: #111827 !important;
        }

        body.dark-mode footer {
            background-color: #030712 !important;
            color: #9ca3af !important;
        }

        /* All white backgrounds to dark */
        body.dark-mode .bg-white,
        body.dark-mode [class*="bg-white"] {
            background-color: #374151 !important;
        }

        /* All gray backgrounds */
        body.dark-mode .bg-gray-50,
        body.dark-mode [class*="bg-gray-50"] {
            background-color: #374151 !important;
        }

        body.dark-mode .bg-gray-100,
        body.dark-mode [class*="bg-gray-100"] {
            background-color: #4b5563 !important;
        }

        body.dark-mode .bg-blue-50 {
            background-color: #111827 !important;
        }

        /* Text colors */
        body.dark-mode .text-gray-800,
        body.dark-mode [class*="text-gray-800"] {
            color: #f9fafb !important;
        }

        body.dark-mode .text-gray-700,
        body.dark-mode [class*="text-gray-700"] {
            color: #d1d5db !important;
        }

        body.dark-mode .text-gray-600,
        body.dark-mode [class*="text-gray-600"] {
            color: #9ca3af !important;
        }

        body.dark-mode .text-gray-500,
        body.dark-mode [class*="text-gray-500"] {
            color: #6b7280 !important;
        }

        body.dark-mode .text-blue-800 {
            color: #60a5fa !important;
        }

        body.dark-mode .text-blue-700 {
            color: #3b82f6 !important;
        }

        /* Borders */
        body.dark-mode .border,
        body.dark-mode .border-gray-200,
        body.dark-mode [class*="border-gray"] {
            border-color: #4b5563 !important;
        }

        /* Hover effects */
        body.dark-mode .hover\:bg-gray-100:hover,
        body.dark-mode [class*="hover:bg-gray-100"]:hover {
            background-color: #4b5563 !important;
        }

        body.dark-mode .hover\:bg-gray-50:hover,
        body.dark-mode [class*="hover:bg-gray-50"]:hover {
            background-color: #374151 !important;
        }

        /* Input fields */
        body.dark-mode input,
        body.dark-mode textarea,
        body.dark-mode select {
            background-color: #374151 !important;
            color: #f9fafb !important;
            border-color: #4b5563 !important;
        }

        body.dark-mode input::placeholder,
        body.dark-mode textarea::placeholder {
            color: #9ca3af !important;
        }

        /* Cards and containers */
        body.dark-mode .shadow,
        body.dark-mode .shadow-md,
        body.dark-mode .shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
        }

        /* Tables */
        body.dark-mode table,
        body.dark-mode th,
        body.dark-mode td {
            background-color: #374151 !important;
            color: #f9fafb !important;
            border-color: #4b5563 !important;
        }

        body.dark-mode .bg-blue-600 {
            background-color: #2563eb !important;
        }

        /* Theme toggle button */
        #themeToggle {
            transition: all 0.3s ease;
        }

        /* Dropdown notifications */
        body.dark-mode #notifDropdown {
            background-color: #1f2937 !important;
            border-color: #4b5563 !important;
        }

        body.dark-mode #notifDropdown h3 {
            color: #f9fafb !important;
        }

        body.dark-mode #notifDropdown .bg-gray-50 {
            background-color: #374151 !important;
        }

        body.dark-mode #notifDropdown .border-b {
            border-color: #4b5563 !important;
        }

        body.dark-mode #notifDropdown .text-gray-800 {
            color: #f9fafb !important;
        }

        body.dark-mode #notifDropdown .text-gray-600 {
            color: #9ca3af !important;
        }

        body.dark-mode #notifDropdown .text-blue-600 {
            color: #60a5fa !important;
        }

        body.dark-mode #notifDropdown .hover\:bg-gray-50:hover {
            background-color: #4b5563 !important;
        }

        /* Profile dropdown */
        body.dark-mode #profileMenu {
            background-color: #1f2937 !important;
            border-color: #4b5563 !important;
        }

        body.dark-mode #profileMenu a,
        body.dark-mode #profileMenu button,
        body.dark-mode #profileMenu span {
            color: #d1d5db !important;
        }

        body.dark-mode #profileMenu .border-t {
            border-color: #4b5563 !important;
        }

        body.dark-mode #profileMenu .hover\:bg-gray-100:hover {
            background-color: #4b5563 !important;
        }

        /* Dashboard Cards */
        body.dark-mode .bg-blue-100 {
            background-color: #1e3a8a !important;
        }

        body.dark-mode .bg-yellow-100 {
            background-color: #92400e !important;
        }

        body.dark-mode .bg-green-100 {
            background-color: #065f46 !important;
        }

        body.dark-mode .bg-red-100 {
            background-color: #991b1b !important;
        }

        body.dark-mode .bg-purple-100 {
            background-color: #581c87 !important;
        }

        body.dark-mode .bg-indigo-100 {
            background-color: #3730a3 !important;
        }

        /* Card text colors */
        body.dark-mode .text-blue-800 {
            color: #dbeafe !important;
        }

        body.dark-mode .text-yellow-800 {
            color: #fef3c7 !important;
        }

        body.dark-mode .text-green-800 {
            color: #d1fae5 !important;
        }

        body.dark-mode .text-red-800 {
            color: #fecaca !important;
        }

        body.dark-mode .text-purple-800 {
            color: #e9d5ff !important;
        }

        body.dark-mode .text-indigo-800 {
            color: #e0e7ff !important;
        }

        /* Card numbers/values */
        body.dark-mode .text-2xl,
        body.dark-mode .text-3xl,
        body.dark-mode .text-4xl {
            color: #f9fafb !important;
        }

        /* Card descriptions */
        body.dark-mode .text-sm {
            color: #d1d5db !important;
        }

        /* All card containers */
        body.dark-mode .rounded-lg,
        body.dark-mode .rounded,
        body.dark-mode .rounded-xl,
        body.dark-mode .rounded-2xl {
            background-color: #374151 !important;
            border-color: #4b5563 !important;
        }

        /* Specific dashboard card styling */
        body.dark-mode a[class*="bg-blue-100"],
        body.dark-mode .bg-blue-100 {
            background-color: #1e3a8a !important;
        }

        body.dark-mode a[class*="bg-yellow-100"],
        body.dark-mode .bg-yellow-100 {
            background-color: #92400e !important;
        }

        body.dark-mode a[class*="bg-green-100"],
        body.dark-mode .bg-green-100 {
            background-color: #065f46 !important;
        }

        body.dark-mode a[class*="bg-red-100"],
        body.dark-mode .bg-red-100 {
            background-color: #991b1b !important;
        }

        body.dark-mode a[class*="bg-purple-100"],
        body.dark-mode .bg-purple-100 {
            background-color: #581c87 !important;
        }

        body.dark-mode a[class*="bg-gray-100"],
        body.dark-mode .bg-gray-100 {
            background-color: #4b5563 !important;
        }

        /* Card text - force white text on dark cards */
        body.dark-mode a[class*="bg-blue-100"] h2,
        body.dark-mode a[class*="bg-blue-100"] p,
        body.dark-mode .bg-blue-100 h2,
        body.dark-mode .bg-blue-100 p {
            color: #ffffff !important;
        }

        body.dark-mode a[class*="bg-yellow-100"] h2,
        body.dark-mode a[class*="bg-yellow-100"] p,
        body.dark-mode .bg-yellow-100 h2,
        body.dark-mode .bg-yellow-100 p {
            color: #ffffff !important;
        }

        body.dark-mode a[class*="bg-green-100"] h2,
        body.dark-mode a[class*="bg-green-100"] p,
        body.dark-mode .bg-green-100 h2,
        body.dark-mode .bg-green-100 p {
            color: #ffffff !important;
        }

        body.dark-mode a[class*="bg-red-100"] h2,
        body.dark-mode a[class*="bg-red-100"] p,
        body.dark-mode .bg-red-100 h2,
        body.dark-mode .bg-red-100 p {
            color: #ffffff !important;
        }

        body.dark-mode a[class*="bg-purple-100"] h2,
        body.dark-mode a[class*="bg-purple-100"] p,
        body.dark-mode .bg-purple-100 h2,
        body.dark-mode .bg-purple-100 p {
            color: #ffffff !important;
        }

        body.dark-mode a[class*="bg-gray-100"] h2,
        body.dark-mode a[class*="bg-gray-100"] p,
        body.dark-mode .bg-gray-100 h2,
        body.dark-mode .bg-gray-100 p {
            color: #ffffff !important;
        }

        /* User management table dark mode */
        body.dark-mode .divide-y {
            border-color: #4b5563 !important;
        }

        body.dark-mode .hover\:bg-gray-50:hover {
            background-color: #374151 !important;
        }

        body.dark-mode .bg-blue-500 {
            background-color: #3b82f6 !important;
        }

        body.dark-mode .bg-red-100 {
            background-color: #7f1d1d !important;
        }

        body.dark-mode .bg-yellow-100 {
            background-color: #92400e !important;
        }

        body.dark-mode .bg-green-100 {
            background-color: #065f46 !important;
        }

        body.dark-mode .text-red-800 {
            color: #fecaca !important;
        }

        body.dark-mode .text-yellow-800 {
            color: #fef3c7 !important;
        }

        body.dark-mode .text-green-800 {
            color: #d1fae5 !important;
        }

        /* Force all elements to inherit theme */
        body.dark-mode * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
        }
    </style>
</head>

<body class="font-sans min-h-screen">

    <!-- Navbar -->
    <nav class="bg-white dark:bg-gray-800 shadow-md sticky top-0 z-50 transition-colors duration-300">
        <div class="max-w-7xl mx-auto px-4 py-4">
            <!-- Mobile-first responsive layout -->
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <a href="{{
                    auth()->user()->role === 'admin' ? route('admin.dashboard') :
                    (auth()->user()->role === 'petugas' ? route('dashboard.petugas') :
                    (auth()->user()->role === 'superadmin' ? route('dashboard.superadmin') :
                    route('user.dashboard')))
                }}" class="text-xl sm:text-2xl font-bold text-blue-700 hover:text-blue-800 transition duration-200">
                    LaporinAja
                </a>

                <!-- Mobile menu button -->
                <button id="mobileMenuButton" class="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                    <svg class="w-6 h-6 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>

                <!-- Desktop navigation -->
                <div class="hidden md:flex items-center space-x-2 lg:space-x-4">

                    <!-- Search Bar - Hidden on small screens -->
                    <div class="hidden lg:block relative text-gray-800 dark:text-gray-200">
                        <input type="text" placeholder="Cari..."
                            class="border dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg py-2 px-4 pl-10 w-48 xl:w-64 focus:outline-blue-500 dark:focus:outline-blue-400 transition-colors duration-300">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <svg class="h-5 w-5 text-gray-600 dark:text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-4.35-4.35m1.15-5.65a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>

                <!--  Notifikasi Dropdown -->
                <div class="relative">
                    <button id="notifButton" class="relative p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg focus:outline-none transition duration-200">
                        <!-- Bell Icon using SVG (more reliable than Font Awesome) -->
                        <svg class="w-6 h-6 text-gray-700 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                        </svg>

                        @if ($notifications->where('read_at', null)->count() > 0)
                            <span id="notificationBadge" class="absolute -top-1 -right-1 inline-flex items-center justify-center w-5 h-5 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
                                {{ $notifications->where('read_at', null)->count() }}
                            </span>
                        @endif
                    </button>


                    <div id="notifDropdown" class="hidden absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto transition-colors duration-300">
                        <!-- Header -->
                        <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 rounded-t-lg">
                            <h3 class="text-sm font-semibold text-gray-800">Notifikasi</h3>
                        </div>

                        <!-- Notifications List -->
                        <div class="divide-y divide-gray-100">
                            @forelse($notifications as $notif)
                                <div class="px-4 py-3 hover:bg-gray-50 transition duration-150 {{ $notif->read_at ? 'opacity-75' : 'bg-blue-50' }}">
                                    <div class="flex items-start gap-3">
                                        <!-- Icon -->
                                        <div class="flex-shrink-0 mt-1">
                                            @if ($notif->read_at)
                                                <!-- Read notification icon -->
                                                @if (str_contains($notif->type, 'LaporanBaru'))
                                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                @elseif (str_contains($notif->type, 'LaporanDiproses'))
                                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                @elseif (str_contains($notif->type, 'LaporanSelesai'))
                                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                @else
                                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                @endif
                                            @else
                                                <!-- Unread notification icon with color coding -->
                                                @if (str_contains($notif->type, 'LaporanBaru'))
                                                    <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                                        <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                            <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                    </div>
                                                @elseif (str_contains($notif->type, 'LaporanDiproses'))
                                                    <div class="w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center">
                                                        <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                            <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                    </div>
                                                @elseif (str_contains($notif->type, 'LaporanSelesai'))
                                                    <div class="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                                                        <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                    </div>
                                                @else
                                                    <div class="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                                                        <div class="w-2 h-2 bg-white rounded-full"></div>
                                                    </div>
                                                @endif
                                            @endif
                                        </div>
                                        <!-- Content -->
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm {{ $notif->read_at ? 'text-gray-600' : 'text-gray-800 font-medium' }}">
                                                {{ $notif->data['pesan'] ?? 'Notifikasi baru' }}
                                            </p>
                                            @if (isset($notif->data['laporan_id']))
                                                @if (auth()->user()->role == 'user')
                                                    <a href="{{ route('user.laporan.show', $notif->data['laporan_id']) }}"
                                                       class="text-xs text-blue-600 hover:text-blue-800 hover:underline mt-1 inline-block">
                                                        Lihat Detail →
                                                    </a>
                                                @else
                                                    <a href="{{ route('laporan.show', $notif->data['laporan_id']) }}"
                                                       class="text-xs text-blue-600 hover:text-blue-800 hover:underline mt-1 inline-block">
                                                        Lihat Detail →
                                                    </a>
                                                @endif
                                            @endif
                                            <p class="text-xs text-gray-500 mt-1">
                                                {{ $notif->created_at->diffForHumans() }}
                                                @if (!$notif->read_at)
                                                    <span class="ml-2 text-blue-600 font-medium">• Baru</span>
                                                @endif
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="px-4 py-8 text-center">
                                    <svg class="w-12 h-12 text-gray-300 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                                    </svg>
                                    <p class="text-sm text-gray-500">Tidak ada notifikasi</p>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!--  Toggle JS Notifikasi -->
                <script>
                    document.addEventListener("DOMContentLoaded", function() {
                        const notifButton = document.getElementById('notifButton');
                        const notifDropdown = document.getElementById('notifDropdown');
                        const notificationBadge = document.getElementById('notificationBadge');

                        // Toggle dropdown when button is clicked
                        notifButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            notifDropdown.classList.toggle('hidden');

                            // Mark notifications as read when dropdown is opened
                            if (!notifDropdown.classList.contains('hidden')) {
                                markNotificationsAsRead();
                            }
                        });

                        // Close dropdown when clicking outside
                        document.addEventListener('click', function(e) {
                            if (!notifButton.contains(e.target) && !notifDropdown.contains(e.target)) {
                                notifDropdown.classList.add('hidden');
                            }
                        });

                        // Function to mark notifications as read
                        function markNotificationsAsRead() {
                            fetch('{{ route("notifications.markAsRead") }}', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                }
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Hide the notification badge
                                    if (notificationBadge) {
                                        notificationBadge.style.display = 'none';
                                    }
                                }
                            })
                            .catch(error => {
                                console.error('Error marking notifications as read:', error);
                            });
                        }
                    });
                </script>

                    <!-- Profile Dropdown -->
                    <div class="relative inline-block text-left">
                        <button type="button"
                            class="inline-flex items-center px-2 sm:px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none text-sm"
                            id="profileButton">
                            <svg class="h-4 w-4 sm:h-5 sm:w-5 sm:mr-2" fill="currentColor" viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                            </svg>
                            <span class="hidden sm:inline">{{ Auth::user()->nama }}</span>
                            <svg class="ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.23 7.21a.75.75 0 011.06.02L10 11.584l3.71-4.355a.75.75 0 111.14.976l-4.25 5a.75.75 0 01-1.14 0l-4.25-5a.75.75 0 01.02-1.06z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>

                    <div id="profileMenu"
                        class="hidden absolute right-0 mt-2 w-48 bg-white border rounded shadow-lg z-50">
                        <a href="{{ route('profile.edit') }}"
                            class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Profil Saya</a>

                        <!-- Theme Toggle -->
                        <div class="px-4 py-2 border-t">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700 text-sm">Theme</span>

                                <!-- Simple Toggle Button -->
                                <button id="themeToggle" onclick="toggleTheme()" class="px-3 py-1 text-xs rounded-full border transition-all duration-300">
                                    <span id="themeText">🌙 Dark</span>
                                </button>
                            </div>
                        </div>

                        <form method="POST" action="{{ route('logout') }}" class="border-t">
                            @csrf
                            <button type="submit"
                                class="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100">Logout</button>
                        </form>
                    </div>
                </div>
                </div>

                <!-- Mobile menu (hidden by default) -->
                <div id="mobileMenu" class="hidden md:hidden mt-4 pb-4 border-t border-gray-200 dark:border-gray-600">
                    <!-- Mobile Search -->
                    <div class="mt-4 relative">
                        <input type="text" placeholder="Cari..."
                            class="w-full border dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg py-2 px-4 pl-10 focus:outline-blue-500 dark:focus:outline-blue-400 transition-colors duration-300">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <svg class="h-5 w-5 text-gray-600 dark:text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-4.35-4.35m1.15-5.65a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>

                    <!-- Mobile User Info -->
                    <div class="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                                </svg>
                            </div>
                            <div>
                                <p class="font-medium text-gray-800 dark:text-gray-200">{{ Auth::user()->nama }}</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400 capitalize">{{ Auth::user()->role }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Navigation Links -->
                    <div class="mt-4 space-y-2">
                        <a href="{{ route('profile.edit') }}"
                           class="block px-3 py-2 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg">
                            📝 Profil Saya
                        </a>

                        <!-- Theme Toggle Mobile -->
                        <div class="px-3 py-2">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700 dark:text-gray-200">🎨 Theme</span>
                                <button id="mobileThemeToggle" onclick="toggleTheme()" class="px-3 py-1 text-xs rounded-full border transition-all duration-300">
                                    <span id="mobileThemeText">🌙 Dark</span>
                                </button>
                            </div>
                        </div>

                        <form method="POST" action="{{ route('logout') }}" class="block">
                            @csrf
                            <button type="submit" class="w-full text-left px-3 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg">
                                🚪 Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Konten -->
    <section class="py-6 sm:py-8 lg:py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @yield('content')

            <div class="mt-8 sm:mt-10 text-center">
                <h2 class="text-lg sm:text-xl font-medium text-blue-800 dark:text-blue-400">Terima kasih telah menjaga kampung kita!</h2>
                <p class="text-gray-600 dark:text-gray-400 mt-2 text-sm sm:text-base">Kontribusimu sangat berharga.</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 dark:bg-gray-950 text-gray-300 dark:text-gray-400 pt-6 sm:pt-10 transition-colors duration-300">
        <div class="max-w-6xl mx-auto px-4 text-center pb-6 sm:pb-10 border-b border-gray-700">
            <h2 class="text-xl sm:text-2xl font-bold text-white mb-2">Berlangganan Informasi</h2>
            <p class="text-gray-400 mb-4 text-sm sm:text-base px-4">Dapatkan update terbaru tentang pengembangan kampung & fitur LaporinAja.</p>
            <div class="flex flex-col sm:flex-row justify-center max-w-md mx-auto gap-2 sm:gap-0">
                <input type="email" placeholder="Masukkan Email Anda"
                    class="w-full px-4 py-2 sm:rounded-l sm:rounded-r-none rounded bg-white text-black focus:outline-blue-500 text-sm">
                <button class="bg-blue-600 text-white px-4 sm:px-6 py-2 sm:rounded-r sm:rounded-l-none rounded hover:bg-blue-700 transition text-sm">Kirim</button>
            </div>
        </div>

        <div class="max-w-6xl mx-auto px-4 py-6 sm:py-10 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8 text-center md:text-left">
            <div>
                <h3 class="text-white text-lg font-semibold mb-4">TENTANG</h3>
                <ul class="space-y-2 text-gray-400">
                    <li><a href="#" class="hover:text-white transition">Apa itu LaporinAja</a></li>
                    <li><a href="#" class="hover:text-white transition">Tim Pengembang</a></li>
                    <li><a href="#" class="hover:text-white transition">Kebijakan Privasi</a></li>
                    <li><a href="#" class="hover:text-white transition">Syarat & Ketentuan</a></li>
                </ul>
            </div>
            <div>
                <h3 class="text-white text-lg font-semibold mb-4">BANTUAN</h3>
                <ul class="space-y-2 text-gray-400">
                    <li><a href="#" class="hover:text-white transition">Hubungi Kami</a></li>
                    <li><a href="#" class="hover:text-white transition">FAQ</a></li>
                    <li><a href="#" class="hover:text-white transition">Panduan Pengguna</a></li>
                </ul>
            </div>
            <div>
                <h3 class="text-white text-lg font-semibold mb-4">FITUR UTAMA</h3>
                <ul class="space-y-2 text-gray-400">
                    <li><a href="#" class="hover:text-white transition">Kirim Laporan</a></li>
                    <li><a href="#" class="hover:text-white transition">Pantau Progres</a></li>
                    <li><a href="#" class="hover:text-white transition">Tanggap Cepat</a></li>
                </ul>
            </div>
        </div>

        <div class="text-center text-gray-500 text-sm py-6 border-t border-gray-800">
            &copy; {{ date('Y') }} LaporinAja. Dibuat oleh Sikami untuk Warga. Seluruh Hak Cipta Dilindungi.
        </div>
    </footer>

    <!-- Success Modal Component -->
    @include('components.success-modal')

    <!-- Manual Theme Toggle Script -->
    <script>
        // Global theme variable
        let currentTheme = localStorage.getItem('theme') || 'light';

        // Function to toggle theme
        function toggleTheme() {
            const body = document.body;
            const html = document.documentElement;
            const themeText = document.getElementById('themeText');
            const themeToggle = document.getElementById('themeToggle');
            const mobileThemeText = document.getElementById('mobileThemeText');
            const mobileThemeToggle = document.getElementById('mobileThemeToggle');

            console.log('Current theme before toggle:', currentTheme);

            function updateAllThemeButtons(isDark) {
                const lightText = '☀️ Light';
                const darkText = '🌙 Dark';

                const buttons = [
                    { text: themeText, toggle: themeToggle },
                    { text: mobileThemeText, toggle: mobileThemeToggle }
                ];

                buttons.forEach(({ text, toggle }) => {
                    if (text && toggle) {
                        if (isDark) {
                            text.innerHTML = lightText;
                            toggle.style.backgroundColor = '#374151';
                            toggle.style.color = 'white';
                            toggle.style.borderColor = '#4b5563';
                        } else {
                            text.innerHTML = darkText;
                            toggle.style.backgroundColor = 'white';
                            toggle.style.color = '#374151';
                            toggle.style.borderColor = '#d1d5db';
                        }
                    }
                });
            }

            if (currentTheme === 'light') {
                // Switch to dark
                currentTheme = 'dark';
                body.classList.add('dark-mode');
                html.classList.add('dark-mode');
                updateAllThemeButtons(true);
                localStorage.setItem('theme', 'dark');
                console.log('Switched to dark mode');

                // Force update all elements
                document.querySelectorAll('*').forEach(el => {
                    el.style.transition = 'all 0.3s ease';
                });

            } else {
                // Switch to light
                currentTheme = 'light';
                body.classList.remove('dark-mode');
                html.classList.remove('dark-mode');
                updateAllThemeButtons(false);
                localStorage.setItem('theme', 'light');
                console.log('Switched to light mode');

                // Force update all elements
                document.querySelectorAll('*').forEach(el => {
                    el.style.transition = 'all 0.3s ease';
                });
            }

            // Force browser to recalculate styles
            setTimeout(() => {
                body.style.display = 'none';
                body.offsetHeight; // Trigger reflow
                body.style.display = '';
                console.log('Theme applied - Background:', window.getComputedStyle(body).backgroundColor);
            }, 50);
        }

        // Initialize theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const body = document.body;
            const themeText = document.getElementById('themeText');
            const themeToggle = document.getElementById('themeToggle');
            const mobileThemeText = document.getElementById('mobileThemeText');
            const mobileThemeToggle = document.getElementById('mobileThemeToggle');

            console.log('Initializing theme:', currentTheme);

            function updateThemeButtons(isDark) {
                const lightText = '☀️ Light';
                const darkText = '🌙 Dark';

                if (isDark) {
                    if (themeText) themeText.innerHTML = lightText;
                    if (mobileThemeText) mobileThemeText.innerHTML = lightText;
                    if (themeToggle) {
                        themeToggle.style.backgroundColor = '#374151';
                        themeToggle.style.color = 'white';
                        themeToggle.style.borderColor = '#4b5563';
                    }
                    if (mobileThemeToggle) {
                        mobileThemeToggle.style.backgroundColor = '#374151';
                        mobileThemeToggle.style.color = 'white';
                        mobileThemeToggle.style.borderColor = '#4b5563';
                    }
                } else {
                    if (themeText) themeText.innerHTML = darkText;
                    if (mobileThemeText) mobileThemeText.innerHTML = darkText;
                    if (themeToggle) {
                        themeToggle.style.backgroundColor = 'white';
                        themeToggle.style.color = '#374151';
                        themeToggle.style.borderColor = '#d1d5db';
                    }
                    if (mobileThemeToggle) {
                        mobileThemeToggle.style.backgroundColor = 'white';
                        mobileThemeToggle.style.color = '#374151';
                        mobileThemeToggle.style.borderColor = '#d1d5db';
                    }
                }
            }

            if (currentTheme === 'dark') {
                body.classList.add('dark-mode');
                updateThemeButtons(true);
                console.log('Applied dark mode on load');
            } else {
                body.classList.remove('dark-mode');
                updateThemeButtons(false);
                console.log('Applied light mode on load');
            }

            // Mobile menu functionality
            const mobileMenuButton = document.getElementById('mobileMenuButton');
            const mobileMenu = document.getElementById('mobileMenu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');

                    // Update button icon
                    const icon = mobileMenuButton.querySelector('svg');
                    if (mobileMenu.classList.contains('hidden')) {
                        // Show hamburger icon
                        icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
                    } else {
                        // Show close icon
                        icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
                    }
                });
            }

            // Debug info
            setTimeout(() => {
                console.log('Final body classes:', body.className);
                console.log('Final body background:', window.getComputedStyle(body).backgroundColor);
            }, 500);
        });
    </script>

    <!-- JS Lokal -->
    <script src="{{ asset('js/main.js') }}"></script>

    <!-- Additional Scripts Section -->
    @yield('scripts')
</body>

</html>
