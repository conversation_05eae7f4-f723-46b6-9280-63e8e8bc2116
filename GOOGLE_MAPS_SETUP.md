# 🗺️ Google Maps API Setup untuk LaporinAja

## Cara Mendapatkan Google Maps API Key

### 1. Buka Google Cloud Console
- Kunjungi: https://console.cloud.google.com/
- Login dengan akun Google Anda

### 2. Buat atau Pilih Project
- Klik "Select a project" di bagian atas
- Pilih project existing atau klik "New Project"
- Beri nama project (contoh: "LaporinAja Maps")
- Klik "Create"

### 3. Enable Google Maps JavaScript API
- Di sidebar kiri, klik "APIs & Services" > "Library"
- Cari "Maps JavaScript API"
- Klik pada "Maps JavaScript API"
- Klik tombol "Enable"

### 4. Buat API Key
- Di sidebar kiri, klik "APIs & Services" > "Credentials"
- <PERSON>lik "Create Credentials" > "API Key"
- Copy API key yang dihasilkan

### 5. (Opsional) Restrict API Key untuk Keamanan
- Klik pada API key yang baru dibuat
- Di bagian "Application restrictions":
  - <PERSON><PERSON>h "HTTP referrers (web sites)"
  - Tambahkan domain Anda (contoh: `localhost:8000/*`, `yourdomain.com/*`)
- Di bagian "API restrictions":
  - <PERSON><PERSON><PERSON> "Restrict key"
  - Centang "Maps JavaScript API"
- Klik "Save"

### 6. Update File .env
```env
GOOGLE_MAPS_API_KEY=YOUR_ACTUAL_API_KEY_HERE
```

### 7. Clear Cache Laravel
```bash
php artisan config:clear
php artisan cache:clear
```

## Troubleshooting

### Error: "This page didn't load Google Maps correctly"
**Penyebab:**
- API key tidak valid
- API key belum di-enable untuk Maps JavaScript API
- Quota API sudah habis
- Domain tidak diizinkan (jika menggunakan restrictions)

**Solusi:**
1. Pastikan API key sudah benar di file `.env`
2. Pastikan Maps JavaScript API sudah di-enable
3. Cek quota di Google Cloud Console
4. Pastikan domain sudah ditambahkan di restrictions (jika ada)

### Error: "RefererNotAllowedMapError"
**Penyebab:**
- Domain tidak diizinkan dalam API key restrictions

**Solusi:**
1. Buka Google Cloud Console > APIs & Services > Credentials
2. Edit API key Anda
3. Tambahkan domain/localhost ke HTTP referrers

### Fallback Mode
Jika Google Maps tidak tersedia, aplikasi akan otomatis:
- Menampilkan pesan error yang user-friendly
- Mengaktifkan input koordinat manual
- Tetap menyimpan data lokasi
- Menyediakan link ke Google Maps eksternal

## Biaya Google Maps API

### Free Tier
- $200 kredit gratis per bulan
- Sekitar 28,000 map loads gratis per bulan
- Cocok untuk aplikasi kecil-menengah

### Estimasi Penggunaan LaporinAja
- Setiap buka form create: 1 map load
- Setiap buka detail laporan: 1 map load
- Estimasi 100 laporan/bulan = ~200 map loads
- Masih jauh di bawah free tier

## Tips Optimasi

1. **Lazy Loading**: Maps hanya dimuat saat dibutuhkan
2. **Error Handling**: Fallback ke input manual jika API gagal
3. **Caching**: Koordinat disimpan di database untuk mengurangi API calls
4. **Restrictions**: Batasi API key hanya untuk domain yang diperlukan

## Alternatif Gratis

Jika tidak ingin menggunakan Google Maps API, bisa menggunakan:

1. **OpenStreetMap + Leaflet** (100% gratis)
2. **Mapbox** (free tier tersedia)
3. **Input koordinat manual** (sudah tersedia sebagai fallback)

## Support

Jika mengalami masalah:
1. Cek console browser untuk error messages
2. Pastikan API key sudah benar
3. Cek quota di Google Cloud Console
4. Gunakan mode fallback untuk input manual
