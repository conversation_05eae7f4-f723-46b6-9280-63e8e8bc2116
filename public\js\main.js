document.addEventListener('DOMContentLoaded', () => {

    // Dropdown Profil
    const profileButton = document.getElementById('profileButton');
    const profileMenu = document.getElementById('profileMenu');

    // Dropdown Notifikasi
    const notifButton = document.getElementById('notifButton');
    const notifMenu = document.getElementById('notifMenu');

    // Toggle Dropdown Profil
    if (profileButton && profileMenu) {
        profileButton.addEventListener('click', (e) => {
            e.stopPropagation();
            profileMenu.classList.toggle('hidden');
            if (notifMenu) notifMenu.classList.add('hidden');
        });
    }

    // Toggle Dropdown Notifikasi
    if (notifButton && notifMenu) {
        notifButton.addEventListener('click', (e) => {
            e.stopPropagation();
            notifMenu.classList.toggle('hidden');
            if (profileMenu) profileMenu.classList.add('hidden');
        });
    }

    // Klik di luar tutup semua dropdown
    document.addEventListener('click', () => {
        if (profileMenu) profileMenu.classList.add('hidden');
        if (notifMenu) notifMenu.classList.add('hidden');
    });
});
