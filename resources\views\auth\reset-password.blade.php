<x-guest-layout>
    <div class="w-full max-w-md bg-white p-8 rounded-xl shadow-lg mt-10">
        
        <h1 class="text-2xl font-bold text-blue-700 mb-2 text-center">Reset Password</h1>
        <p class="text-sm text-gray-600 mb-6 text-center">Masukkan password baru untuk akun <PERSON>a</p>

        <form method="POST" action="{{ route('password.store') }}" class="space-y-4">
            @csrf

            <!-- Password Reset Token -->
            <input type="hidden" name="token" value="{{ $request->route('token') }}">

            <!-- Email Address -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                <input id="email" type="email" name="email" value="{{ old('email', $request->email) }}" required autofocus autocomplete="username"
                    class="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100 text-black focus:outline-none focus:ring-2 focus:ring-blue-500" 
                    readonly />
                @if ($errors->get('email'))
                    <div class="mt-2 text-sm text-red-600">
                        @foreach ($errors->get('email') as $error)
                            <p>{{ $error }}</p>
                        @endforeach
                    </div>
                @endif
            </div>

            <!-- Password -->
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700">Password Baru</label>
                <input id="password" type="password" name="password" required autocomplete="new-password"
                    class="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500" 
                    placeholder="Masukkan password baru" />
                @if ($errors->get('password'))
                    <div class="mt-2 text-sm text-red-600">
                        @foreach ($errors->get('password') as $error)
                            <p>{{ $error }}</p>
                        @endforeach
                    </div>
                @endif
            </div>

            <!-- Confirm Password -->
            <div>
                <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Konfirmasi Password</label>
                <input id="password_confirmation" type="password" name="password_confirmation" required autocomplete="new-password"
                    class="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500" 
                    placeholder="Ulangi password baru" />
                @if ($errors->get('password_confirmation'))
                    <div class="mt-2 text-sm text-red-600">
                        @foreach ($errors->get('password_confirmation') as $error)
                            <p>{{ $error }}</p>
                        @endforeach
                    </div>
                @endif
            </div>

            <div>
                <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                    Reset Password
                </button>
            </div>
        </form>
        
        <p class="text-sm text-center mt-4">
            Ingat password Anda? <a href="{{ route('login') }}" class="text-blue-600 hover:underline">Kembali ke Login</a>
        </p>
    </div>
</x-guest-layout>
