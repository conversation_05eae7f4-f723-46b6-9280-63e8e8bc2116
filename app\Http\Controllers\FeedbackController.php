<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Feedback;
use Illuminate\Support\Facades\Auth;

class FeedbackController extends Controller
{
    /**
     * Simpan feedback dari pengguna yang login.
     */
    public function store(Request $request)
    {
        $request->validate([
            'pesan' => 'required|string',
            'rating' => 'required|integer|min:1|max:5',
        ]);

        Feedback::create([
            'user_id' => Auth::id(),
            'role' => Auth::user()->role ?? 'anonymous',
            'pesan' => $request->pesan,
            'rating' => $request->rating,
        ]);

        return back()->with('success', 'Feedback berhasil dikirim!');
    }

    /**
     * Simpan feedback dari Call Center (umum/tanpa login).
     */
    public function storeCallCenter(Request $request)
    {
        $request->validate([
            'nama' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'pesan' => 'required|string',
        ]);

        Feedback::create([
            'nama' => $request->nama,
            'email' => $request->email,
            'role' => 'callcenter',
            'pesan' => $request->pesan,
        ]);

        return back()->with('success', 'Feedback berhasil dikirim!');
    }

    /**
     * Tampilkan daftar semua feedback untuk admin/petugas.
     */
    public function index()
    {
        $feedbacks = Feedback::latest()->paginate(10);
        return view('feedback.index', compact('feedbacks'));
    }
}
