<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Laporan extends Model
{
    use HasFactory;

    protected $fillable = [
        'judul',
        'isi',
        'status',
        'prioritas',
        'user_id',
        'bukti',
        'bukti_awal',
        'kategori',
        'alamat',
        'rt',
        'rw',
        'latitude',
        'longitude'
    ];


    // Relasi ke tabel users
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Relasi ke tabel ratings
    // app/Models/Laporan.php

    public function rating()
    {
        return $this->hasOne(Rating::class);
    }
}
