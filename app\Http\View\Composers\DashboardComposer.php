<?php

namespace App\Http\View\Composers;

use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class DashboardComposer
{
    /**
     * Bind data to the view.
     */
    public function compose(View $view): void
    {
        // Only add notifications if user is authenticated
        if (Auth::check()) {
            $notifications = Auth::user()->notifications()->latest()->get();
            $view->with('notifications', $notifications);
        } else {
            // Provide empty collection for unauthenticated users
            $view->with('notifications', collect());
        }
    }
}
