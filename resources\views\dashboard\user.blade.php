@extends('dashboard.template')

@section('title', $title)

@section('content')

    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <h1 class="text-xl sm:text-2xl font-bold">Selamat Datang, {{ auth()->user()->name }}</h1>
        <a href="{{ route('lapor.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded shadow text-center sm:text-left whitespace-nowrap">
            + Buat <PERSON>an
        </a>
    </div>

    {{-- Kartu Statistik --}}
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8">

        <a href="{{ route('user.laporan.index') }}"
            class="flex flex-col justify-center items-center p-4 sm:p-6 lg:p-8 min-h-[140px] sm:min-h-[160px] bg-blue-100 dark:bg-blue-900/30 rounded-2xl shadow hover:shadow-lg transition transform hover:scale-105">

            <div class="text-3xl sm:text-4xl mb-2 sm:mb-4">📝</div>
            <h2 class="text-lg sm:text-xl lg:text-2xl font-bold text-blue-800 dark:text-blue-300 mb-1 sm:mb-2 text-center">Total Laporan</h2>
            <p class="text-gray-700 dark:text-gray-300 text-sm sm:text-base lg:text-lg text-center">{{ $totalLaporan }} laporan yang Anda kirim</p>
        </a>

        <a href="{{ route('user.laporan.index', ['status' => 'diproses']) }}"
            class="flex flex-col justify-center items-center p-4 sm:p-6 lg:p-8 min-h-[140px] sm:min-h-[160px] bg-yellow-100 dark:bg-yellow-900/30 rounded-2xl shadow hover:shadow-lg transition transform hover:scale-105">

            <div class="text-3xl sm:text-4xl mb-2 sm:mb-4">⏳</div>
            <h2 class="text-lg sm:text-xl font-bold text-yellow-800 dark:text-yellow-300 mb-1 sm:mb-2 text-center">Sedang Diproses</h2>
            <p class="text-gray-700 dark:text-gray-300 text-sm sm:text-base lg:text-lg text-center">{{ $laporanDiproses }} laporan diproses</p>
        </a>

        <a href="{{ route('user.laporan.index', ['status' => 'selesai']) }}"
            class="flex flex-col justify-center items-center p-4 sm:p-6 lg:p-8 min-h-[140px] sm:min-h-[160px] bg-green-100 dark:bg-green-900/30 rounded-2xl shadow hover:shadow-lg transition transform hover:scale-105 sm:col-span-2 lg:col-span-1">

            <div class="text-3xl sm:text-4xl mb-2 sm:mb-4">✅</div>
            <h2 class="text-lg sm:text-xl font-bold text-green-800 dark:text-green-300 mb-1 sm:mb-2 text-center">Selesai</h2>
            <p class="text-gray-700 dark:text-gray-300 text-sm sm:text-base lg:text-lg text-center">{{ $laporanSelesai }} laporan selesai</p>
        </a>

    </div>


    {{-- Daftar Laporan --}}
    <div>
        <h2 class="text-lg sm:text-xl font-semibold mb-4">Laporan Saya</h2>
        @forelse ($laporans as $laporan)
            <div class="p-4 sm:p-6 mb-4 border dark:border-gray-600 rounded-xl shadow hover:shadow-md bg-white dark:bg-gray-800 transition-colors duration-300">
                <h3 class="font-bold text-base sm:text-lg mb-2 text-gray-900 dark:text-gray-100">{{ $laporan->judul }}</h3>
                <p class="text-gray-700 dark:text-gray-300 mb-3 text-sm sm:text-base line-clamp-3">{{ $laporan->isi }}</p>

                <div class="flex flex-wrap items-center gap-2 mb-3">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Status:</span>
                    @if ($laporan->status == 'belum_diproses')
                        <span class="px-2 py-1 text-xs sm:text-sm bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300 rounded-full font-semibold">Belum Diproses</span>
                    @elseif ($laporan->status == 'diproses')
                        <span class="px-2 py-1 text-xs sm:text-sm bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300 rounded-full font-semibold">Sedang Diproses</span>
                    @elseif ($laporan->status == 'selesai')
                        <span class="px-2 py-1 text-xs sm:text-sm bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300 rounded-full font-semibold">Selesai</span>
                    @endif
                </div>

                @if ($laporan->bukti)
                    <div class="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p class="font-semibold text-sm text-gray-700 dark:text-gray-300 mb-2">Bukti Laporan:</p>
                        <div class="flex flex-col sm:flex-row sm:items-center gap-3">
                            <img src="{{ asset('storage/' . $laporan->bukti) }}" class="w-full sm:w-32 lg:w-48 rounded-lg object-cover">
                            <a href="{{ asset('storage/' . $laporan->bukti) }}" download
                                class="inline-flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Download Bukti
                            </a>
                        </div>
                    </div>
                @endif

                <div class="mt-4 pt-3 border-t dark:border-gray-600">
                    <a href="{{ route('user.laporan.show', $laporan->id) }}"
                        class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium text-sm transition-colors">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        Lihat Detail
                    </a>
                </div>
            </div>
            {{-- Form Feedback --}}
            <div class="mt-8 p-4 sm:p-6 bg-gray-50 dark:bg-gray-800 rounded-xl">
                <form action="{{ route('feedback.store') }}" method="POST"
                    class="w-full max-w-3xl mx-auto bg-white dark:bg-gray-700 p-4 sm:p-6 rounded-lg shadow space-y-4">
                    @csrf

                    <h2 class="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-200">Beri Feedback Anda</h2>

                    <div>
                        <label for="pesan" class="block text-gray-700 dark:text-gray-300 font-medium mb-2 text-sm sm:text-base">Pesan:</label>
                        <textarea name="pesan" id="pesan"
                            class="w-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors duration-300 text-sm sm:text-base"
                            rows="4" placeholder="Tulis pesan, saran, atau kritik Anda di sini..."></textarea>
                    </div>

                    <div>
                        <label for="rating" class="block text-gray-700 dark:text-gray-300 font-medium mb-2 text-sm sm:text-base">Rating:</label>
                        <select name="rating" id="rating" required
                            class="w-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors duration-300 text-sm sm:text-base">
                            <option value="">Pilih Rating</option>
                            <option value="1">⭐ (1 - Sangat Buruk)</option>
                            <option value="2">⭐⭐ (2 - Buruk)</option>
                            <option value="3">⭐⭐⭐ (3 - Cukup)</option>
                            <option value="4">⭐⭐⭐⭐ (4 - Baik)</option>
                            <option value="5">⭐⭐⭐⭐⭐ (5 - Sangat Baik)</option>
                        </select>
                    </div>

                    <button type="submit"
                        class="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white font-semibold py-3 rounded-lg shadow transition duration-200 text-sm sm:text-base">
                        Kirim Feedback
                    </button>
                </form>
            </div>


        @empty
            <div class="text-center py-12">
                <div class="text-6xl mb-4">📝</div>
                <h3 class="text-lg sm:text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">Belum Ada Laporan</h3>
                <p class="text-gray-500 dark:text-gray-500 mb-6 text-sm sm:text-base">Anda belum pernah mengirim laporan. Mulai buat laporan pertama Anda!</p>
                <a href="{{ route('lapor.create') }}"
                   class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm sm:text-base">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Buat Laporan Pertama
                </a>
            </div>
        @endforelse
    </div>

@endsection
