<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('feedback', function (Blueprint $table) {
            $table->string('role')->nullable()->after('user_id');
            $table->text('pesan')->nullable()->after('role'); // Sekalian pastikan kolom pesan ada
        });
    }

    public function down(): void
    {
        Schema::table('feedback', function (Blueprint $table) {
            $table->dropColumn(['role', 'pesan']);
        });
    }
};
