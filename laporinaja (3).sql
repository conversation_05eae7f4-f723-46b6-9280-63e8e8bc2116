-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 25, 2025 at 05:45 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `laporinaja`
--

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nama` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `feedback`
--

CREATE TABLE `feedback` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kategori_laporans`
--

CREATE TABLE `kategori_laporans` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `laporans`
--

CREATE TABLE `laporans` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `judul` varchar(255) NOT NULL,
  `isi` text NOT NULL,
  `status` enum('belum_diproses','diproses','selesai') NOT NULL DEFAULT 'belum_diproses',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `kategori` varchar(255) DEFAULT NULL,
  `bukti` varchar(255) DEFAULT NULL,
  `bukti_awal` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `laporans`
--

INSERT INTO `laporans` (`id`, `user_id`, `judul`, `isi`, `status`, `created_at`, `updated_at`, `kategori`, `bukti`, `bukti_awal`) VALUES
(1, 1, 'sampah', 'ada sampah menumpuk', 'selesai', '2025-06-24 18:58:44', '2025-06-24 19:13:36', NULL, 'bukti/usXFADcDHgnijN8tA0EvVrOLRTALQptqIGlbAcyF.jpg', NULL),
(2, 1, 'jalan rusak', 'jalan rusak', 'selesai', '2025-06-25 07:31:27', '2025-06-25 07:34:13', NULL, 'bukti/oVpyMWgKTkPrL9JY7XtBsqVJOeaa5h9ubtjPXVly.jpg', NULL),
(3, 1, 'maling ayam', 'diketahui didin maling ayam di rt 2', 'selesai', '2025-06-25 07:46:54', '2025-06-25 08:12:12', NULL, 'bukti/EL4hvaVefzzLMMg71AVn5yzuVwijZ1s082Fw4iUR.png', 'bukti/Z7IbpyA0mpmwtBlNhc5VyH7ZE36LgG5x2aM5X1Oz.png'),
(4, 1, 'na', 'apa', 'selesai', '2025-06-25 08:24:06', '2025-06-25 08:25:29', NULL, 'bukti/BOfe1oRP4wepHpcAx6Rij9GS2fpjCLPUMUxyQ5qL.png', 'bukti/kic0YXk1Gn0dZMP7RDuDNAfo1NqMnKIHvzmgNBFY.png');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(17, '2014_10_12_000000_create_users_table', 1),
(18, '2014_10_12_100000_create_password_reset_tokens_table', 1),
(19, '2019_08_19_000000_create_failed_jobs_table', 1),
(20, '2019_12_14_000001_create_personal_access_tokens_table', 1),
(21, '2025_05_23_163139_create_penggunas_table', 1),
(22, '2025_05_23_163152_create_petugas_table', 1),
(23, '2025_05_23_163156_create_kategori_laporans_table', 1),
(24, '2025_05_23_163201_create_laporans_table', 1),
(25, '2025_05_23_163205_create_verifikasis_table', 1),
(26, '2025_05_23_163207_create_penugasans_table', 1),
(27, '2025_05_23_163210_create_update_penanganans_table', 1),
(28, '2025_05_23_163214_create_notifikasis_table', 1),
(29, '2025_05_23_163217_create_feedback_table', 1),
(30, '2025_05_23_163219_create_statistiks_table', 1),
(31, '2025_06_13_164440_create_pengguna_table', 1),
(32, '2025_06_13_165505_create_admins_table', 1),
(34, '2025_06_23_154249_add_status_to_laporans_table', 2),
(35, '2025_06_25_005604_add_kategori_to_laporans_table', 3),
(36, '2025_06_25_010941_add_bukti_and_status_to_laporans_table', 4),
(37, '2025_06_25_013558_create_notifications_table', 5),
(38, '2025_06_25_145440_add_bukti_awal_to_laporans_table', 6);

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `notifiable_type` varchar(255) NOT NULL,
  `notifiable_id` bigint(20) UNSIGNED NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `type`, `notifiable_type`, `notifiable_id`, `data`, `read_at`, `created_at`, `updated_at`) VALUES
('0500dbac-3e09-4546-8e53-b19d0fda5a76', 'App\\Notifications\\LaporanDiproses', 'App\\Models\\User', 1, '{\"pesan\":\"Laporan \\\"sampah\\\" sedang diproses.\",\"laporan_id\":1}', NULL, '2025-06-24 19:11:31', '2025-06-24 19:11:31'),
('15da7ab3-c197-4805-9f22-40e250baf688', 'App\\Notifications\\LaporanDiproses', 'App\\Models\\User', 1, '{\"pesan\":\"Laporan \\\"na\\\" sedang diproses.\",\"laporan_id\":4}', NULL, '2025-06-25 08:25:04', '2025-06-25 08:25:04'),
('3cb50d1b-bae1-4be9-b485-27f0ea525ef5', 'App\\Notifications\\LaporanDiproses', 'App\\Models\\User', 1, '{\"pesan\":\"Laporan \\\"maling ayam\\\" sedang diproses.\",\"laporan_id\":3}', NULL, '2025-06-25 08:11:37', '2025-06-25 08:11:37'),
('a818bce7-0306-4d03-9402-1047bf9055ee', 'App\\Notifications\\LaporanSelesai', 'App\\Models\\User', 1, '{\"pesan\":\"Laporan \\\"sampah\\\" telah selesai diproses.\",\"laporan_id\":1}', NULL, '2025-06-24 19:13:43', '2025-06-24 19:13:43'),
('c2f275da-5cdf-4cf3-be2b-9c362c431ab8', 'App\\Notifications\\LaporanDiproses', 'App\\Models\\User', 1, '{\"pesan\":\"Laporan \\\"jalan rusak\\\" sedang diproses.\",\"laporan_id\":2}', NULL, '2025-06-25 07:33:19', '2025-06-25 07:33:19'),
('d5b2d486-0ca5-4d57-9e59-5a52a950f3d2', 'App\\Notifications\\LaporanSelesai', 'App\\Models\\User', 1, '{\"pesan\":\"Laporan \\\"maling ayam\\\" telah selesai diproses.\",\"laporan_id\":3}', NULL, '2025-06-25 08:12:19', '2025-06-25 08:12:19'),
('faf405b3-8045-45ca-9163-8262c98b3485', 'App\\Notifications\\LaporanSelesai', 'App\\Models\\User', 1, '{\"pesan\":\"Laporan \\\"na\\\" telah selesai diproses.\",\"laporan_id\":4}', NULL, '2025-06-25 08:25:36', '2025-06-25 08:25:36'),
('fc288518-8c9d-41d5-9b14-283d60e5ad54', 'App\\Notifications\\LaporanSelesai', 'App\\Models\\User', 1, '{\"pesan\":\"Laporan \\\"jalan rusak\\\" telah selesai diproses.\",\"laporan_id\":2}', NULL, '2025-06-25 07:34:19', '2025-06-25 07:34:19');

-- --------------------------------------------------------

--
-- Table structure for table `notifikasis`
--

CREATE TABLE `notifikasis` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `password_reset_tokens`
--

INSERT INTO `password_reset_tokens` (`email`, `token`, `created_at`) VALUES
('<EMAIL>', '$2y$12$D97wxfEI8TvPWR1Xgyyv8e0WNZNqiPnzdKx4eSsyK1YXm2md3e5Z.', '2025-06-23 04:35:17');

-- --------------------------------------------------------

--
-- Table structure for table `pengguna`
--

CREATE TABLE `pengguna` (
  `id_pengguna` bigint(20) UNSIGNED NOT NULL,
  `nama` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `no_hp` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `penggunas`
--

CREATE TABLE `penggunas` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `penugasans`
--

CREATE TABLE `penugasans` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `petugas`
--

CREATE TABLE `petugas` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `statistiks`
--

CREATE TABLE `statistiks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `update_penanganans`
--

CREATE TABLE `update_penanganans` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `role` varchar(255) NOT NULL DEFAULT 'user',
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `role`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'yogi', '<EMAIL>', NULL, '$2y$12$dbDsfTb89xqdTTvq5Wqo4ewNz5JKpw0JQmEM1B8URKSs3jm4O1bRq', 'user', NULL, '2025-06-22 07:28:05', '2025-06-23 03:35:37'),
(2, 'TJHGFDS', '<EMAIL>', NULL, '$2y$12$Pu4LibFpQYdLFI63B4nxDuT9.MN/t2BgexC.X8Vn61M.Mz7nXzEDq', 'petugas', NULL, '2025-06-22 08:55:59', '2025-06-22 08:55:59'),
(3, 'admin 1', '<EMAIL>', NULL, '$2y$12$8.l5RduO/nlTl3RkAt4NpOt5x0KCH5oAJC48H1GspMz094EaykTQq', 'admin', 'e2DtWUAZLQl5RAvjUcsBE64CGXGlMnvhEJMbwy5Qk9RLqRHKdlb6p79i06Aa', '2025-06-22 09:28:05', '2025-06-22 09:28:05'),
(4, 'yogs', '<EMAIL>', NULL, '$2y$12$yfZp8OByLMuFZq6RnMoqQ.mTWpMImBUEGlT4Y4GJ9cVopZe5Kx1tO', 'user', NULL, '2025-06-22 18:06:14', '2025-06-22 18:06:14'),
(5, 'din12', '<EMAIL>', NULL, '$2y$12$7yHH6EW12TYJ.T3zvP4oI.oo447H5iUVO2Z9ohfnzAOj/f52V40jK', 'user', NULL, '2025-06-23 20:56:11', '2025-06-23 20:56:11'),
(6, 'petugas', '<EMAIL>', NULL, '$2y$12$YWs6ALSz4EN53W.W1g4jZ.bIyeJvSUGLMS5BjmIBRFfIsfbDrh0GC', 'petugas', NULL, '2025-06-24 18:30:16', '2025-06-24 18:30:16');

-- --------------------------------------------------------

--
-- Table structure for table `verifikasis`
--

CREATE TABLE `verifikasis` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admins_email_unique` (`email`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `feedback`
--
ALTER TABLE `feedback`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kategori_laporans`
--
ALTER TABLE `kategori_laporans`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `laporans`
--
ALTER TABLE `laporans`
  ADD PRIMARY KEY (`id`),
  ADD KEY `laporans_user_id_foreign` (`user_id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`);

--
-- Indexes for table `notifikasis`
--
ALTER TABLE `notifikasis`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `pengguna`
--
ALTER TABLE `pengguna`
  ADD PRIMARY KEY (`id_pengguna`),
  ADD UNIQUE KEY `pengguna_email_unique` (`email`);

--
-- Indexes for table `penggunas`
--
ALTER TABLE `penggunas`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `penugasans`
--
ALTER TABLE `penugasans`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `petugas`
--
ALTER TABLE `petugas`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `statistiks`
--
ALTER TABLE `statistiks`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `update_penanganans`
--
ALTER TABLE `update_penanganans`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- Indexes for table `verifikasis`
--
ALTER TABLE `verifikasis`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `feedback`
--
ALTER TABLE `feedback`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kategori_laporans`
--
ALTER TABLE `kategori_laporans`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `laporans`
--
ALTER TABLE `laporans`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=39;

--
-- AUTO_INCREMENT for table `notifikasis`
--
ALTER TABLE `notifikasis`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pengguna`
--
ALTER TABLE `pengguna`
  MODIFY `id_pengguna` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `penggunas`
--
ALTER TABLE `penggunas`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `penugasans`
--
ALTER TABLE `penugasans`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `petugas`
--
ALTER TABLE `petugas`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `statistiks`
--
ALTER TABLE `statistiks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `update_penanganans`
--
ALTER TABLE `update_penanganans`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `verifikasis`
--
ALTER TABLE `verifikasis`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `laporans`
--
ALTER TABLE `laporans`
  ADD CONSTRAINT `laporans_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
