<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $role = $request->query('role');
        $currentUser = auth()->user();

        // If role=petugas is requested and current user is petugas, show only current user
        if ($role === 'petugas' && $currentUser->role === 'petugas') {
            $users = collect([$currentUser]); // Convert single user to collection
            $users = new \Illuminate\Pagination\LengthAwarePaginator(
                $users,
                1,
                10,
                1,
                ['path' => $request->url(), 'query' => $request->query()]
            );
        } else {
            // For admin or other cases, show filtered users or all users
            $query = User::latest();

            if ($role) {
                $query->where('role', $role);
            }

            $users = $query->paginate(10);
        }

        return view('pengguna.index', [
            'users' => $users,
            'role' => $role
        ]);
    }

    /**
     * Show the form for creating a new petugas account.
     */
    public function createPetugas()
    {
        return view('pengguna.create-petugas');
    }

    /**
     * Store a newly created petugas account.
     */
    public function storePetugas(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:' . User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'petugas',
        ]);

        return redirect()->route('pengguna.index', ['role' => 'petugas'])
                        ->with('success_title', 'Berhasil')
                        ->with('success_message', 'Akun petugas berhasil dibuat!')
                        ->with('success_type', 'petugas');
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        // Only admin can edit users
        if (auth()->user()->role !== 'admin') {
            abort(403, 'Unauthorized action.');
        }

        return view('pengguna.edit', compact('user'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, User $user)
    {
        // Only admin can update users
        if (auth()->user()->role !== 'admin') {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'role' => ['required', 'in:user,petugas,admin'],
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
        ]);

        $user->name = $request->name;
        $user->email = $request->email;
        $user->role = $request->role;

        if ($request->filled('password')) {
            $user->password = Hash::make($request->password);
        }

        $user->save();

        return redirect()->route('pengguna.index')
                        ->with('success_title', 'Berhasil')
                        ->with('success_message', 'Data pengguna berhasil diperbarui!')
                        ->with('success_type', 'update');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user)
    {
        // Only admin can delete users
        if (auth()->user()->role !== 'admin') {
            abort(403, 'Unauthorized action.');
        }

        // Prevent admin from deleting themselves
        if ($user->id === auth()->id()) {
            return redirect()->route('pengguna.index')
                            ->with('error_title', 'Gagal')
                            ->with('error_message', 'Anda tidak dapat menghapus akun sendiri!')
                            ->with('error_type', 'delete');
        }

        $userName = $user->name;
        $user->delete();

        return redirect()->route('pengguna.index')
                        ->with('success_title', 'Berhasil')
                        ->with('success_message', "Akun {$userName} berhasil dihapus!")
                        ->with('success_type', 'delete');
    }
}
