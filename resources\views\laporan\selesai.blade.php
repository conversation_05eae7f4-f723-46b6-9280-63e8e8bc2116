@extends('dashboard.template')

@section('title', 'Seles<PERSON><PERSON>')

@section('content')
<h1 class="text-2xl font-bold mb-4"><PERSON><PERSON><PERSON><PERSON></h1>

<div class="p-6 bg-white shadow rounded-lg">
    <form action="{{ url('/laporan/'.$laporan->id.'/selesai') }}" method="POST" enctype="multipart/form-data">
        @csrf

        <div class="mb-4">
            <label class="block font-semibold mb-1">B<PERSON><PERSON> Penye<PERSON>n (Foto)</label>
            <input type="file" name="bukti" required class="border p-2 w-full rounded" accept="image/*">
            @error('bukti')
                <p class="text-red-500 text-sm">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label class="block font-semibold mb-1">Catatan / Deskripsi Penyelesaian</label>
            <textarea name="catatan_selesai" rows="4" class="border p-2 w-full rounded" placeholder="Tulis deskripsi atau catatan penyelesaian..."></textarea>
            @error('catatan_selesai')
                <p class="text-red-500 text-sm">{{ $message }}</p>
            @enderror
        </div>

        <div class="flex gap-2">
            <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                Tandai Selesai
            </button>
            <a href="{{ url()->previous() }}" class="inline-block px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition">
                ← Kembali
            </a>
        </div>
    </form>
</div>
@endsection
