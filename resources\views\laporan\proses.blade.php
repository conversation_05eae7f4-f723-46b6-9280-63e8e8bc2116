@extends('dashboard.template')

@section('title', '<PERSON><PERSON>')

@section('content')
<h1 class="text-2xl font-bold mb-4"><PERSON><PERSON></h1>

<div class="p-6 bg-white shadow rounded-lg">
    <form action="{{ url('/laporan/'.$laporan->id.'/proses') }}" method="POST" enctype="multipart/form-data">
        @csrf

        <div class="mb-4">
            <label class="block font-semibold mb-1"><PERSON><PERSON><PERSON> (Foto/Video)</label>
            <input type="file" name="bukti_awal" required class="border p-2 w-full rounded" accept="image/*,video/*">
            @error('bukti_awal')
                <p class="text-red-500 text-sm">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label class="block font-semibold mb-1">Catatan / Deskripsi Proses</label>
            <textarea name="catatan_proses" rows="4" class="border p-2 w-full rounded" placeholder="Tulis catatan proses atau keterangan tambahan..."></textarea>
            @error('catatan_proses')
                <p class="text-red-500 text-sm">{{ $message }}</p>
            @enderror
        </div>

        <div class="flex gap-2">
            <button type="submit" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                Tandai Diproses
            </button>
            <a href="{{ url()->previous() }}" class="inline-block px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition">
                ← Kembali
            </a>
        </div>
    </form>
</div>
@endsection
