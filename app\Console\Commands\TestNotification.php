<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Laporan;
use App\Notifications\LaporanBaru;

class TestNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test notification system for new laporan';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing notification system...');
        
        // Check if there are petugas users
        $petugasCount = User::where('role', 'petugas')->count();
        $this->info("Found {$petugasCount} petugas users");
        
        if ($petugasCount === 0) {
            $this->warn('No petugas users found. Notifications will not be sent.');
            return;
        }
        
        // Get the latest laporan
        $latestLaporan = Laporan::latest()->first();
        
        if (!$latestLaporan) {
            $this->warn('No laporan found to test with.');
            return;
        }
        
        $this->info("Testing with laporan: {$latestLaporan->judul}");
        
        // Send notification to all petugas
        $petugas = User::where('role', 'petugas')->get();
        foreach ($petugas as $petugasUser) {
            $petugasUser->notify(new LaporanBaru($latestLaporan));
            $this->info("Notification sent to: {$petugasUser->name}");
        }
        
        $this->info('Test completed successfully!');
    }
}
